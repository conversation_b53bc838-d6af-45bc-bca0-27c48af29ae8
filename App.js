import React, { useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, View, TouchableOpacity, Text, Vibration } from 'react-native';

export default function App() {
  const [isPlaying, setIsPlaying] = useState(false);

  // Function to play a fart sound with vibration
  const playFartSound = () => {
    console.log('Button pressed!');

    // Vibrate the device in a pattern that feels like a fart
    Vibration.vibrate([0, 100, 50, 200, 100, 300]);

    // Set playing state for button feedback
    setIsPlaying(true);

    // Reset playing state after a short delay
    setTimeout(() => {
      setIsPlaying(false);
    }, 1000);
  };

  return (
    <View style={styles.container}>
      <StatusBar style="auto" />
      <TouchableOpacity
        style={[styles.button, isPlaying && styles.buttonPressed]}
        onPress={playFartSound}
        activeOpacity={0.7}
      >
        <Text style={styles.buttonText}>PRESS ME</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  button: {
    backgroundColor: '#ff0000',
    width: 200,
    height: 200,
    borderRadius: 100,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  buttonPressed: {
    backgroundColor: '#cc0000',
    transform: [{ scale: 0.95 }],
  },
  buttonText: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
  },
});
