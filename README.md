# RecipeSafe

A web application for saving and organizing recipes.

## Running the Simplified Demo

For a quick demonstration without setting up the full environment, you can use the simplified demo:

1. Make sure you have Node.js installed
2. Run the demo script:

```bash
# On Windows
run-demo.bat

# On macOS/Linux
npm install express --save
node simple-demo.js
```

3. Open your browser and go to http://localhost:5000
4. Use the password "happy days" to log in

This simplified demo includes:
- Basic password authentication
- Viewing recipe list
- Viewing recipe details
- Mock recipe import functionality

## Full Application Setup

### Prerequisites

To run the full application, you need:

- Node.js (v20 or later)
- npm (v10 or later)
- PostgreSQL (v16 recommended)

### Environment Setup

Create a `.env` file in the root directory with the following variables:

```
DATABASE_URL=postgres://postgres:postgres@localhost:5432/recipesafe
SESSION_SECRET=your-secret-key
# Add your Gemini API key for enhanced recipe parsing
GEMINI_API_KEY=your-gemini-api-key
```

### Installation

1. Install dependencies:

```bash
npm install
```

2. Set up the database:

```bash
npm run db:push
```

3. Start the development server:

```bash
npm run dev
```

The application will be available at http://localhost:5000

### Using Docker

If you prefer to use Docker, you can use the provided Docker Compose configuration:

```bash
docker-compose up
```

This will start both the application and a PostgreSQL database.

## Authentication

The application uses a simple password authentication system. The default password is:

```
happy days
```

## Features

- Import recipes from URLs
- Create recipes manually
- Organize recipes into collections
- Search recipes
- View recipe details
