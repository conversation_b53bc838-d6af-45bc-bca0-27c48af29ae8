import React, { useState, useEffect } from 'react';
import { StyleSheet, View, TouchableOpacity, Text } from 'react-native';
import { Audio } from 'expo-av';

// Array of fart sound URLs
const FART_SOUNDS = [
  'https://assets.mixkit.co/active_storage/sfx/212/212-preview.mp3',
  'https://assets.mixkit.co/active_storage/sfx/531/531-preview.mp3',
  'https://assets.mixkit.co/active_storage/sfx/1356/1356-preview.mp3',
  'https://assets.mixkit.co/active_storage/sfx/2869/2869-preview.mp3',
  'https://assets.mixkit.co/active_storage/sfx/2868/2868-preview.mp3',
];

export default function FartButtonScreen() {
  const [sound, setSound] = useState<Audio.Sound | null>(null);

  // Clean up the sound when component unmounts
  useEffect(() => {
    return sound
      ? () => {
          sound.unloadAsync();
        }
      : undefined;
  }, [sound]);

  // Function to play a random fart sound
  const playRandomFartSound = async () => {
    // Unload the previous sound if it exists
    if (sound) {
      await sound.unloadAsync();
    }

    // Get a random sound URL from the array
    const randomIndex = Math.floor(Math.random() * FART_SOUNDS.length);
    const soundUrl = FART_SOUNDS[randomIndex];

    try {
      // Load the sound
      const { sound: newSound } = await Audio.Sound.createAsync(
        { uri: soundUrl },
        { shouldPlay: true }
      );
      
      // Set the sound state
      setSound(newSound);
      
      // Play the sound
      await newSound.playAsync();
    } catch (error) {
      console.error('Error playing sound:', error);
    }
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.button}
        onPress={playRandomFartSound}
        activeOpacity={0.7}
      >
        <Text style={styles.buttonText}>PRESS ME</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  button: {
    backgroundColor: '#ff0000',
    width: 200,
    height: 200,
    borderRadius: 100,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  buttonText: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
  },
});
