import { Switch, Route } from "wouter";
import NotFound from "@/pages/not-found";
import HomePage from "@/pages/home-page";
import RecipeDetailPage from "@/pages/recipe-detail-page";
import TestHomePage from "@/pages/test-home";
import PasswordPage from "@/pages/password-page";
import { SimpleProtectedRoute } from "./lib/simple-protected-route";
import { SimpleAuthProvider } from "@/hooks/use-simple-auth";

function Router() {
  return (
    <Switch>
      <Route path="/password" component={PasswordPage} />
      <SimpleProtectedRoute path="/" component={HomePage} />
      <SimpleProtectedRoute path="/home" component={HomePage} />
      <SimpleProtectedRoute path="/recipe/:id" component={RecipeDetailPage} />
      <Route path="/test" component={TestHomePage} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <SimpleAuthProvider>
      <Router />
    </SimpleAuthProvider>
  );
}

export default App;
