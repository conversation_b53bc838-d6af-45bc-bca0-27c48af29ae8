import React, { useState } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { queryClient } from '@/lib/queryClient';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Check, Folder, Plus, Loader2 } from 'lucide-react';
import type { Folder as FolderType } from '@shared/schema';

interface AddToCollectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  recipeId: number;
}

export default function AddToCollectionDialog({
  isOpen,
  onClose,
  recipeId,
}: AddToCollectionDialogProps) {
  const { toast } = useToast();
  const [newFolderName, setNewFolderName] = useState('');
  const [selectedFolderId, setSelectedFolderId] = useState<number | null>(null);
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);

  // Fetch folders
  const { data: folders, isLoading: isFoldersLoading } = useQuery<FolderType[]>({
    queryKey: ['/api/folders'],
  });

  // Add recipe to folder mutation
  const addToFolderMutation = useMutation({
    mutationFn: async () => {
      if (!selectedFolderId) return;
      
      await apiRequest('POST', `/api/folders/${selectedFolderId}/recipes/${recipeId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/folders'] });
      
      toast({
        title: 'Recipe added to collection',
        description: 'Recipe has been added to the selected collection',
      });
      
      onClose();
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Create new folder mutation
  const createFolderMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest('POST', '/api/folders', { name: newFolderName });
      return await response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/folders'] });
      setSelectedFolderId(data.id);
      setIsCreatingFolder(false);
      setNewFolderName('');
      
      toast({
        title: 'Collection created',
        description: `"${newFolderName}" collection has been created`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error creating collection',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const handleAddToCollection = () => {
    if (!selectedFolderId) {
      toast({
        title: 'No collection selected',
        description: 'Please select a collection first',
        variant: 'destructive',
      });
      return;
    }

    addToFolderMutation.mutate();
  };

  const handleCreateFolder = () => {
    if (!newFolderName.trim()) {
      toast({
        title: 'Enter a name',
        description: 'Please enter a name for your new collection',
        variant: 'destructive',
      });
      return;
    }

    createFolderMutation.mutate();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Add to Collection</DialogTitle>
          <DialogDescription>
            Choose a collection to add this recipe to, or create a new one.
          </DialogDescription>
        </DialogHeader>
        
        {isFoldersLoading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <>
            {/* Folders list */}
            {folders && folders.length > 0 ? (
              <ScrollArea className="max-h-[200px] overflow-auto">
                <div className="space-y-2">
                  {folders.map((folder) => (
                    <div
                      key={folder.id}
                      className={`flex items-center justify-between p-3 rounded-md cursor-pointer transition-colors ${
                        selectedFolderId === folder.id
                          ? 'bg-primary/10 border border-primary/30'
                          : 'hover:bg-neutral-100 border border-transparent'
                      }`}
                      onClick={() => setSelectedFolderId(folder.id)}
                    >
                      <div className="flex items-center">
                        <Folder className="h-5 w-5 mr-3 text-neutral-500" />
                        <span>{folder.name}</span>
                      </div>
                      {selectedFolderId === folder.id && (
                        <Check className="h-5 w-5 text-primary" />
                      )}
                    </div>
                  ))}
                </div>
              </ScrollArea>
            ) : (
              <div className="py-4 text-center text-neutral-500">
                You don't have any collections yet. Create one below.
              </div>
            )}

            {/* Create new folder */}
            {isCreatingFolder ? (
              <div className="mt-4 flex items-center space-x-2">
                <input
                  type="text"
                  value={newFolderName}
                  onChange={(e) => setNewFolderName(e.target.value)}
                  placeholder="Enter collection name"
                  className="px-3 py-2 border rounded-md flex-1 focus:outline-none focus:ring-1 focus:ring-primary"
                />
                <Button 
                  size="sm" 
                  onClick={handleCreateFolder}
                  disabled={createFolderMutation.isPending}
                >
                  {createFolderMutation.isPending ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : 'Create'}
                </Button>
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={() => setIsCreatingFolder(false)}
                >
                  Cancel
                </Button>
              </div>
            ) : (
              <Button
                variant="outline"
                className="mt-4 w-full"
                onClick={() => setIsCreatingFolder(true)}
              >
                <Plus className="h-4 w-4 mr-2" />
                Create New Collection
              </Button>
            )}
          </>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button 
            onClick={handleAddToCollection}
            disabled={!selectedFolderId || addToFolderMutation.isPending}
          >
            {addToFolderMutation.isPending ? (
              <Loader2 className="h-4 w-4 mr-1 animate-spin" />
            ) : null}
            Add to Collection
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}