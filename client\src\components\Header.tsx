import { useState } from "react";
import { useLocation } from "wouter";
import { useSimpleAuth } from "@/hooks/use-simple-auth";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Plus, Search, Menu, LogOut, Home } from "lucide-react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";

interface HeaderProps {
  onMenuClick: () => void;
  onSearch?: (query: string) => void;
  onImportClick?: () => void;
}

export default function Header({ onMenuClick, onSearch, onImportClick }: HeaderProps) {
  const [, navigate] = useLocation();
  const { logout } = useSimpleAuth();
  const [searchQuery, setSearchQuery] = useState("");
  
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (onSearch) {
      onSearch(searchQuery);
    }
  };
  
  const handleLogout = () => {
    logout();
  };
  
  return (
    <header className="bg-white border-b border-neutral-200 sticky top-0 z-50 shadow-sm">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <a href="/" className="flex items-center">
              <svg className="h-8 w-8 text-primary" fill="currentColor" viewBox="0 0 24 24">
                <path d="M8.8 15.9L4.2 11.3C3.9 11 3.9 10.5 4.2 10.2C4.5 9.9 5 9.9 5.3 10.2L9.9 14.8C10.2 15.1 10.2 15.6 9.9 15.9C9.6 16.2 9.1 16.2 8.8 15.9Z" />
                <path d="M9.9 15.9L18.4 7.4C18.7 7.1 18.7 6.6 18.4 6.3C18.1 6 17.6 6 17.3 6.3L8.8 14.8C8.5 15.1 8.5 15.6 8.8 15.9C9.1 16.2 9.6 16.2 9.9 15.9Z" />
                <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" fill="none" strokeWidth="2" />
              </svg>
              <span className="ml-2 text-xl font-heading font-bold text-neutral-900">Recipe Safe</span>
            </a>
          </div>

          {/* Search (visible on larger screens) */}
          <div className="hidden md:flex flex-1 max-w-md mx-4">
            <form onSubmit={handleSearch} className="relative w-full">
              <Input
                type="text"
                placeholder="Search recipes, ingredients..."
                className="pl-10 pr-4 py-2 w-full"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <Search className="w-5 h-5 text-neutral-500 absolute left-3 top-2.5" />
            </form>
          </div>

          {/* Right-side navigation */}
          <div className="flex items-center space-x-4">
            {/* Import Button */}
            <Button 
              className="hidden sm:flex items-center bg-primary hover:bg-primary-dark/90"
              onClick={onImportClick}
            >
              <Plus className="w-5 h-5 mr-1" />
              Import Recipe
            </Button>

            {/* User menu dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="bg-primary/10 text-primary">
                      F
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">Family</p>
                    <p className="text-xs leading-none text-muted-foreground">
                      Shared Recipe Collection
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => navigate("/")}>
                  <Home className="mr-2 h-4 w-4" />
                  <span>Home</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout}>
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Mobile menu button */}
            <Button variant="ghost" size="icon" className="md:hidden" onClick={onMenuClick}>
              <Menu className="h-6 w-6" />
            </Button>
          </div>
        </div>

        {/* Mobile search (visible on small screens) */}
        <div className="md:hidden pb-3 px-2">
          <form onSubmit={handleSearch} className="relative">
            <Input
              type="text"
              placeholder="Search recipes, ingredients..."
              className="pl-10 pr-4 py-2 w-full"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Search className="w-5 h-5 text-neutral-500 absolute left-3 top-2.5" />
          </form>
        </div>
      </div>
    </header>
  );
}
