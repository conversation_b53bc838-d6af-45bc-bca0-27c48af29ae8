import { ReactNode, useState } from "react";
import Header from "./Header";
import Sidebar from "./Sidebar";
import MobileNavigation from "./MobileNavigation";

interface LayoutProps {
  children: ReactNode;
  selectedFolder?: number | null;
  onFolderSelect?: (folderId: number | null) => void;
  onSearch?: (query: string) => void;
  onImportClick?: () => void;
  onCreateClick?: () => void;
}

export default function Layout({
  children,
  selectedFolder,
  onFolderSelect,
  onSearch,
  onImportClick,
  onCreateClick
}: LayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  
  return (
    <div className="flex flex-col h-screen">
      <Header 
        onMenuClick={() => setSidebarOpen(!sidebarOpen)} 
        onSearch={onSearch}
        onImportClick={onImportClick}
      />
      
      <div className="flex-1 flex overflow-hidden">
        <Sidebar 
          isOpen={sidebarOpen} 
          onClose={() => setSidebarOpen(false)}
          selectedFolder={selectedFolder}
          onFolderSelect={onFolderSelect}
          onCreateClick={onCreateClick}
        />
        
        <main className="flex-1 overflow-y-auto bg-neutral-50 p-4 sm:p-6 lg:p-8">
          {children}
        </main>
      </div>
      
      <MobileNavigation onCreateClick={onCreateClick} onImportClick={onImportClick} />
    </div>
  );
}
