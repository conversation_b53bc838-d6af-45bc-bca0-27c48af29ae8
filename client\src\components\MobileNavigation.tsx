import { useLocation } from "wouter";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { HomeIcon, SearchIcon, PlusIcon, FolderIcon, UserIcon } from "lucide-react";

interface MobileNavigationProps {
  onCreateClick?: () => void;
  onImportClick?: () => void;
}

export default function MobileNavigation({ onCreateClick, onImportClick }: MobileNavigationProps) {
  const [location, navigate] = useLocation();
  
  return (
    <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-neutral-200 py-2 px-4 flex justify-between items-center z-40">
      <Button
        variant="ghost"
        size="icon"
        className="flex flex-col items-center text-xs pt-0 pb-0 h-auto"
        onClick={() => navigate("/")}
      >
        <HomeIcon className={cn("h-6 w-6", location === "/" ? "text-primary" : "text-neutral-500")} />
        <span className={cn("mt-1", location === "/" ? "text-primary" : "text-neutral-500")}>Home</span>
      </Button>
      
      <Button
        variant="ghost"
        size="icon"
        className="flex flex-col items-center text-xs pt-0 pb-0 h-auto"
      >
        <SearchIcon className="h-6 w-6 text-neutral-500" />
        <span className="mt-1 text-neutral-500">Search</span>
      </Button>
      
      <div className="flex flex-col items-center relative">
        <Button
          className="h-12 w-12 rounded-full bg-primary text-white -mt-6 flex items-center justify-center"
          onClick={onImportClick}
        >
          <PlusIcon className="h-6 w-6" />
        </Button>
      </div>
      
      <Button
        variant="ghost"
        size="icon"
        className="flex flex-col items-center text-xs pt-0 pb-0 h-auto"
      >
        <FolderIcon className="h-6 w-6 text-neutral-500" />
        <span className="mt-1 text-neutral-500">Folders</span>
      </Button>
      
      <Button
        variant="ghost"
        size="icon"
        className="flex flex-col items-center text-xs pt-0 pb-0 h-auto"
      >
        <UserIcon className="h-6 w-6 text-neutral-500" />
        <span className="mt-1 text-neutral-500">Profile</span>
      </Button>
    </div>
  );
}
