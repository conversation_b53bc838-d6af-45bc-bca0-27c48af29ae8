import { useLocation } from "wouter";
import { useMutation } from "@tanstack/react-query";
import { queryClient } from "@/lib/queryClient";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { Recipe } from "@shared/schema";
import { Button } from "@/components/ui/button";
import { 
  Card, 
  CardContent, 
  CardFooter 
} from "@/components/ui/card";
import { Heart, Clock, List } from "lucide-react";
import { cn } from "@/lib/utils";
import { format } from "date-fns";

interface RecipeCardProps {
  recipe: Recipe;
  viewMode: "grid" | "list";
}

export default function RecipeCard({ recipe, viewMode }: RecipeCardProps) {
  const [, navigate] = useLocation();
  const { toast } = useToast();
  
  // Placeholder function for toggling favorite
  const toggleFavoriteMutation = useMutation({
    mutationFn: async () => {
      // TODO: Implement favorite toggle
      await apiRequest("POST", `/api/favorites/${recipe.id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/favorites'] });
      queryClient.invalidateQueries({ queryKey: ['/api/recipes'] });
      toast({
        title: "Added to favorites",
        description: "Recipe has been added to your favorites",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  const viewRecipe = () => {
    navigate(`/recipe/${recipe.id}`);
  };
  
  if (viewMode === "list") {
    return (
      <Card 
        className="flex overflow-hidden hover:shadow-md cursor-pointer transition-shadow"
        onClick={viewRecipe}
      >
        <div className="w-32 md:w-48 h-auto bg-neutral-200 relative">
          {recipe.imageUrl ? (
            <img 
              src={recipe.imageUrl} 
              alt={recipe.title} 
              className="w-full h-full object-cover" 
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <List className="h-8 w-8 text-neutral-400" />
            </div>
          )}
        </div>
        <div className="flex-1 p-4">
          <h3 className="text-lg font-heading font-semibold mb-1 text-neutral-900">{recipe.title}</h3>
          
          <div className="flex items-center text-xs text-neutral-500 mb-2">
            <span className="flex items-center">
              <Clock className="h-4 w-4 mr-1" />
              <span>{recipe.prepTime || "Not specified"}</span>
            </span>
          </div>
          
          {recipe.description && (
            <p className="text-sm text-neutral-600 line-clamp-2 mb-3">{recipe.description}</p>
          )}
          
          <div className="flex justify-between items-center">
            <span className="text-xs text-neutral-500">
              Added {format(new Date(recipe.createdAt), 'MMM d, yyyy')}
            </span>
            <Button 
              variant="link" 
              className="text-primary p-0" 
              onClick={(e) => {
                e.stopPropagation(); // Prevent card click event
                viewRecipe();
              }}
            >
              View Recipe
            </Button>
          </div>
        </div>
      </Card>
    );
  }
  
  return (
    <Card 
      className="recipe-card overflow-hidden hover:shadow-md cursor-pointer transition-shadow"
      onClick={viewRecipe}
    >
      <div className="relative h-48">
        {recipe.imageUrl ? (
          <img 
            src={recipe.imageUrl} 
            alt={recipe.title} 
            className="w-full h-full object-cover" 
          />
        ) : (
          <div className="flex items-center justify-center h-full bg-neutral-100">
            <List className="h-12 w-12 text-neutral-300" />
          </div>
        )}
        <div className="absolute top-0 right-0 p-2">
          <Button 
            variant="ghost" 
            size="icon" 
            className="rounded-full bg-white/80 hover:bg-white"
            onClick={(e) => {
              e.stopPropagation(); // Prevent card click event
              toggleFavoriteMutation.mutate();
            }}
          >
            <Heart className={cn("h-5 w-5", recipe.isFavorite ? "fill-primary text-primary" : "text-neutral-500")} />
          </Button>
        </div>
      </div>
      
      <CardContent className="p-4">
        <h3 className="text-lg font-heading font-semibold mb-1 text-neutral-900">{recipe.title}</h3>
        
        <div className="flex items-center text-xs text-neutral-500 mb-2">
          <span className="flex items-center">
            <Clock className="h-4 w-4 mr-1" />
            <span>{recipe.prepTime || "Not specified"}</span>
          </span>
        </div>
        
        {recipe.description && (
          <p className="text-sm text-neutral-600 line-clamp-2 mb-3">{recipe.description}</p>
        )}
        
        <div className="flex justify-between items-center">
          <span className="text-xs text-neutral-500">
            Added {format(new Date(recipe.createdAt), 'MMM d, yyyy')}
          </span>
          <Button 
            variant="link" 
            className="text-primary p-0" 
            onClick={(e) => {
              e.stopPropagation(); // Prevent card click event
              viewRecipe();
            }}
          >
            View Recipe
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
