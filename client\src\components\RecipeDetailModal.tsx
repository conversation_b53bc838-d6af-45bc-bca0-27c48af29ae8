import { useEffect, useState } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { queryClient } from '@/lib/queryClient';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, useFieldArray } from 'react-hook-form';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Loader2, Plus, X, Trash2 } from 'lucide-react';

interface RecipeDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  recipeId?: number;
  isEditing?: boolean;
}

const recipeFormSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  imageUrl: z.string().optional(),
  prepTime: z.string().optional(),
  cookTime: z.string().optional(),
  sourceUrl: z.string().optional(),
  notes: z.string().optional(),
  ingredients: z.array(
    z.object({
      name: z.string().min(1, 'Ingredient name is required'),
      quantity: z.string().optional(),
      unit: z.string().optional(),
    })
  ),
  steps: z.array(
    z.object({
      instruction: z.string().min(1, 'Instruction is required'),
      stepNumber: z.number(),
    })
  ),
  folder: z.string().optional(),
  tags: z.array(z.string()),
});

type RecipeFormValues = z.infer<typeof recipeFormSchema>;

export default function RecipeDetailModal({
  isOpen,
  onClose,
  recipeId,
  isEditing = false,
}: RecipeDetailModalProps) {
  const { toast } = useToast();
  const [newTag, setNewTag] = useState('');
  
  // Fetch recipe if editing
  const { data: recipeData, isLoading: isLoadingRecipe } = useQuery({
    queryKey: ['/api/recipes', recipeId],
    enabled: isOpen && isEditing && !!recipeId,
  });
  
  // Get folders for the dropdown
  const { data: folders } = useQuery({
    queryKey: ['/api/folders'],
    enabled: isOpen,
  });
  
  // Setup form
  const form = useForm<RecipeFormValues>({
    resolver: zodResolver(recipeFormSchema),
    defaultValues: {
      title: '',
      description: '',
      imageUrl: '',
      prepTime: '',
      cookTime: '',
      sourceUrl: '',
      notes: '',
      ingredients: [{ name: '', quantity: '', unit: '' }],
      steps: [{ instruction: '', stepNumber: 1 }],
      tags: [],
      folder: '',
    },
  });
  
  // Set form values if editing
  useEffect(() => {
    if (isEditing && recipeData) {
      const { recipe, ingredients, steps, tags } = recipeData;
      
      form.reset({
        title: recipe.title,
        description: recipe.description || '',
        imageUrl: recipe.imageUrl || '',
        prepTime: recipe.prepTime || '',
        cookTime: recipe.cookTime || '',
        sourceUrl: recipe.sourceUrl || '',
        notes: recipe.notes || '',
        ingredients: ingredients.map((ingredient: any) => ({
          name: ingredient.name,
          quantity: ingredient.quantity || '',
          unit: ingredient.unit || '',
        })) || [{ name: '', quantity: '', unit: '' }],
        steps: steps.map((step: any) => ({
          instruction: step.instruction,
          stepNumber: step.stepNumber,
        })) || [{ instruction: '', stepNumber: 1 }],
        tags: tags?.map((tag: any) => tag.name) || [],
        folder: '',
      });
    }
  }, [isEditing, recipeData, form]);
  
  // Field arrays for ingredients and steps
  const { fields: ingredientFields, append: appendIngredient, remove: removeIngredient } = useFieldArray({
    control: form.control,
    name: 'ingredients',
  });
  
  const { fields: stepFields, append: appendStep, remove: removeStep } = useFieldArray({
    control: form.control,
    name: 'steps',
  });
  
  // Create recipe mutation
  const createRecipeMutation = useMutation({
    mutationFn: async (values: RecipeFormValues) => {
      // Transform form data to API format
      const recipeData = {
        recipe: {
          title: values.title,
          description: values.description,
          imageUrl: values.imageUrl,
          prepTime: values.prepTime,
          cookTime: values.cookTime,
          sourceUrl: values.sourceUrl,
          notes: values.notes,
        },
        ingredients: values.ingredients,
        steps: values.steps.map((step, index) => ({
          ...step,
          stepNumber: index + 1,
        })),
        tags: values.tags,
      };
      
      // Create or update recipe
      if (isEditing && recipeId) {
        const res = await apiRequest("PUT", `/api/recipes/${recipeId}`, recipeData);
        return await res.json();
      } else {
        const res = await apiRequest("POST", "/api/recipes", recipeData);
        return await res.json();
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/recipes'] });
      if (recipeId) {
        queryClient.invalidateQueries({ queryKey: ['/api/recipes', recipeId] });
      }
      
      toast({
        title: isEditing ? "Recipe updated" : "Recipe created",
        description: isEditing 
          ? "Your recipe has been updated successfully" 
          : "Your recipe has been added to your collection",
      });
      
      // Reset and close
      form.reset();
      onClose();
    },
    onError: (error: Error) => {
      toast({
        title: isEditing ? "Failed to update recipe" : "Failed to create recipe",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  const onSubmit = (values: RecipeFormValues) => {
    createRecipeMutation.mutate(values);
  };
  
  const handleAddTag = () => {
    if (newTag.trim()) {
      const currentTags = form.getValues('tags');
      if (!currentTags.includes(newTag.trim())) {
        form.setValue('tags', [...currentTags, newTag.trim()]);
      }
      setNewTag('');
    }
  };
  
  const handleRemoveTag = (index: number) => {
    const currentTags = form.getValues('tags');
    form.setValue('tags', currentTags.filter((_, i) => i !== index));
  };
  
  if (isLoadingRecipe) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-4xl max-h-screen overflow-y-auto">
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Loading recipe...</span>
          </div>
        </DialogContent>
      </Dialog>
    );
  }
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-4xl max-h-screen overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Edit Recipe" : "Create New Recipe"}</DialogTitle>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Recipe Title</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter recipe title" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Brief description of the recipe" 
                          rows={3}
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="prepTime"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Prep Time</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g. 20 min" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="cookTime"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Cook Time</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g. 30 min" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <FormField
                  control={form.control}
                  name="imageUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Image URL</FormLabel>
                      <FormControl>
                        <Input placeholder="URL to recipe image" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="sourceUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Source URL</FormLabel>
                      <FormControl>
                        <Input placeholder="Where did you find this recipe?" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="tags"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tags</FormLabel>
                      <div className="flex flex-wrap gap-2 border rounded-md p-2 min-h-24">
                        {field.value.map((tag, index) => (
                          <Badge key={index} className="bg-neutral-100 text-neutral-800 hover:bg-neutral-200">
                            {tag}
                            <button 
                              type="button"
                              className="ml-1 text-neutral-500 hover:text-neutral-800"
                              onClick={() => handleRemoveTag(index)}
                            >
                              ×
                            </button>
                          </Badge>
                        ))}
                        <div className="flex">
                          <Input 
                            value={newTag}
                            onChange={(e) => setNewTag(e.target.value)}
                            placeholder="Add a tag"
                            className="w-32"
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                e.preventDefault();
                                handleAddTag();
                              }
                            }}
                          />
                          <Button 
                            type="button" 
                            variant="ghost" 
                            size="sm"
                            onClick={handleAddTag}
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="folder"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Add to Collection</FormLabel>
                      <Select 
                        onValueChange={field.onChange} 
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a collection" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">None</SelectItem>
                          {folders?.map((folder: any) => (
                            <SelectItem key={folder.id} value={folder.id.toString()}>
                              {folder.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Notes</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Any additional notes or tips" 
                          rows={4}
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">Ingredients</h3>
              <div className="space-y-3">
                {ingredientFields.map((field, index) => (
                  <div key={field.id} className="flex space-x-2">
                    <FormField
                      control={form.control}
                      name={`ingredients.${index}.quantity`}
                      render={({ field }) => (
                        <FormItem className="w-1/5">
                          <FormControl>
                            <Input placeholder="Qty" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name={`ingredients.${index}.unit`}
                      render={({ field }) => (
                        <FormItem className="w-1/5">
                          <FormControl>
                            <Input placeholder="Unit" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name={`ingredients.${index}.name`}
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormControl>
                            <Input placeholder="Ingredient name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => removeIngredient(index)}
                      disabled={ingredientFields.length <= 1}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => appendIngredient({ name: '', quantity: '', unit: '' })}
                  className="mt-2"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Ingredient
                </Button>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">Instructions</h3>
              <div className="space-y-3">
                {stepFields.map((field, index) => (
                  <div key={field.id} className="flex space-x-2">
                    <div className="min-w-10 py-2 text-center font-medium">
                      {index + 1}.
                    </div>
                    
                    <FormField
                      control={form.control}
                      name={`steps.${index}.instruction`}
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormControl>
                            <Textarea 
                              placeholder="Instruction step" 
                              rows={2} 
                              {...field} 
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <input 
                      type="hidden" 
                      {...form.register(`steps.${index}.stepNumber`)} 
                      value={index + 1}
                    />
                    
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => removeStep(index)}
                      disabled={stepFields.length <= 1}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => appendStep({ instruction: '', stepNumber: stepFields.length + 1 })}
                  className="mt-2"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Step
                </Button>
              </div>
            </div>
            
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-primary hover:bg-primary-dark"
                disabled={createRecipeMutation.isPending}
              >
                {createRecipeMutation.isPending && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                {isEditing ? "Update Recipe" : "Create Recipe"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
