import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { queryClient } from "@/lib/queryClient";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { Folder } from "@shared/schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Loader2, Check, Link, Facebook, Instagram } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface RecipeImportModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const urlSchema = z.object({
  url: z.string().url("Please enter a valid URL"),
});

type UrlFormValues = z.infer<typeof urlSchema>;

const IMPORT_STEPS = ["Enter URL", "Review Recipe", "Save"];

export default function RecipeImportModal({ isOpen, onClose }: RecipeImportModalProps) {
  const { toast } = useToast();
  const [currentStep, setCurrentStep] = useState(0);
  const [parsedRecipe, setParsedRecipe] = useState<any>(null);
  const [selectedFolder, setSelectedFolder] = useState<string>("none");
  
  // Get folders for the dropdown
  const { data: folders } = useQuery<Folder[]>({
    queryKey: ['/api/folders'],
    enabled: isOpen && currentStep === 2,
  });
  
  // URL form
  const urlForm = useForm<UrlFormValues>({
    resolver: zodResolver(urlSchema),
    defaultValues: {
      url: "",
    },
  });
  
  // Parse recipe mutation
  const parseRecipeMutation = useMutation({
    mutationFn: async (values: UrlFormValues) => {
      const res = await apiRequest("POST", "/api/recipes/import", values);
      return await res.json();
    },
    onSuccess: (data) => {
      setParsedRecipe(data);
      setCurrentStep(1);
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to import recipe",
        description: error.message || "Please check the URL and try again",
        variant: "destructive",
      });
    },
  });
  
  // Save recipe mutation
  const saveRecipeMutation = useMutation({
    mutationFn: async () => {
      if (!parsedRecipe) return;
      
      const recipeData = {
        recipe: {
          ...parsedRecipe.recipe,
          folderId: selectedFolder && selectedFolder !== "none" ? parseInt(selectedFolder) : undefined,
        },
        ingredients: parsedRecipe.ingredients,
        steps: parsedRecipe.steps,
        tags: parsedRecipe.tags,
      };
      
      const res = await apiRequest("POST", "/api/recipes", recipeData);
      return await res.json();
    },
    onSuccess: (data) => {
      console.log("Recipe saved successfully:", data);
      
      // Force refetch all recipes to update the UI
      queryClient.invalidateQueries({ queryKey: ['/api/recipes'] });
      queryClient.refetchQueries({ queryKey: ['/api/recipes'] });
      
      if (selectedFolder && selectedFolder !== "none") {
        queryClient.invalidateQueries({ queryKey: ['/api/folders'] });
      }
      
      toast({
        title: "Recipe imported successfully",
        description: "Your recipe has been added to your collection",
      });
      
      // Reset and close
      setCurrentStep(0);
      setParsedRecipe(null);
      urlForm.reset();
      onClose();
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to save recipe",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Handle URL form submission
  const onSubmitUrl = (values: UrlFormValues) => {
    parseRecipeMutation.mutate(values);
  };
  
  // Handle saving the recipe
  const handleSaveRecipe = () => {
    saveRecipeMutation.mutate();
  };
  
  const handleClose = () => {
    setCurrentStep(0);
    setParsedRecipe(null);
    urlForm.reset();
    onClose();
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-3xl max-h-screen overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Import Recipe</DialogTitle>
          <DialogDescription>
            Import a recipe from any website by pasting the URL below.
          </DialogDescription>
        </DialogHeader>
        
        {/* Steps indicator */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            {IMPORT_STEPS.map((step, index) => (
              <div key={index} className="flex items-center relative mx-4">
                <div className={`rounded-full transition duration-500 ease-in-out h-8 w-8 py-1 border-2 flex items-center justify-center ${
                  currentStep >= index 
                    ? "border-primary bg-primary text-white" 
                    : "border-neutral-300 bg-white text-neutral-600"
                }`}>
                  {currentStep > index ? <Check className="h-4 w-4" /> : index + 1}
                </div>
                <div className={`absolute top-0 -ml-6 text-xs font-medium mt-9 w-24 text-center ${
                  currentStep >= index ? "text-primary" : "text-neutral-600"
                }`}>
                  {step}
                </div>
                {index < IMPORT_STEPS.length - 1 && (
                  <div className={`w-24 mx-2 border-t-2 transition duration-500 ease-in-out ${
                    currentStep > index ? "border-primary" : "border-neutral-300"
                  }`} style={{ minWidth: '80px' }}></div>
                )}
              </div>
            ))}
          </div>
        </div>
        
        {/* Step 1: URL Input */}
        {currentStep === 0 && (
          <div>
            <Form {...urlForm}>
              <form onSubmit={urlForm.handleSubmit(onSubmitUrl)} className="space-y-4">
                <FormField
                  control={urlForm.control}
                  name="url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Recipe URL</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="https://example.com/recipe" 
                          {...field} 
                          className="p-3"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <p className="text-sm text-neutral-500">
                  Paste any public recipe URL from websites, blogs, or social media. We'll try to extract the recipe details automatically.
                </p>
                
                <div className="flex flex-wrap gap-2">
                  <Badge className="inline-flex items-center bg-neutral-100 text-neutral-800 hover:bg-neutral-200 px-3 py-1">
                    <Facebook className="mr-1 h-3 w-3" />
                    Facebook
                  </Badge>
                  <Badge className="inline-flex items-center bg-neutral-100 text-neutral-800 hover:bg-neutral-200 px-3 py-1">
                    <Instagram className="mr-1 h-3 w-3" />
                    Instagram
                  </Badge>
                  <Badge className="bg-neutral-100 text-neutral-800 hover:bg-neutral-200 px-3 py-1">
                    allrecipes.com
                  </Badge>
                  <Badge className="bg-neutral-100 text-neutral-800 hover:bg-neutral-200 px-3 py-1">
                    foodnetwork.com
                  </Badge>
                  <Badge className="bg-neutral-100 text-neutral-800 hover:bg-neutral-200 px-3 py-1">
                    epicurious.com
                  </Badge>
                  <Badge className="bg-neutral-100 text-neutral-800 hover:bg-neutral-200 px-3 py-1">
                    + many more
                  </Badge>
                </div>
                
                <DialogFooter className="mt-6">
                  <Button variant="outline" onClick={handleClose}>
                    Cancel
                  </Button>
                  <Button 
                    type="submit" 
                    className="bg-primary hover:bg-primary-dark"
                    disabled={parseRecipeMutation.isPending}
                  >
                    {parseRecipeMutation.isPending && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    Import Recipe
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </div>
        )}
        
        {/* Step 2: Preview & Edit */}
        {currentStep === 1 && parsedRecipe && (
          <div className="space-y-6">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h3 className="text-lg font-medium mb-2">Recipe Details</h3>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Title</label>
                    <Input 
                      value={parsedRecipe.recipe.title} 
                      onChange={(e) => setParsedRecipe({
                        ...parsedRecipe,
                        recipe: {
                          ...parsedRecipe.recipe,
                          title: e.target.value
                        }
                      })}
                    />
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium">Description</label>
                    <Textarea 
                      value={parsedRecipe.recipe.description || ''} 
                      onChange={(e) => setParsedRecipe({
                        ...parsedRecipe,
                        recipe: {
                          ...parsedRecipe.recipe,
                          description: e.target.value
                        }
                      })}
                      rows={3}
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium">Prep Time</label>
                      <Input 
                        value={parsedRecipe.recipe.prepTime || ''} 
                        onChange={(e) => setParsedRecipe({
                          ...parsedRecipe,
                          recipe: {
                            ...parsedRecipe.recipe,
                            prepTime: e.target.value
                          }
                        })}
                        placeholder="e.g. 20 min"
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Cook Time</label>
                      <Input 
                        value={parsedRecipe.recipe.cookTime || ''} 
                        onChange={(e) => setParsedRecipe({
                          ...parsedRecipe,
                          recipe: {
                            ...parsedRecipe.recipe,
                            cookTime: e.target.value
                          }
                        })}
                        placeholder="e.g. 30 min"
                      />
                    </div>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">Image</h3>
                {parsedRecipe.recipe.imageUrl ? (
                  <div className="relative h-48 rounded border border-neutral-200 overflow-hidden">
                    <img 
                      src={parsedRecipe.recipe.imageUrl} 
                      alt={parsedRecipe.recipe.title} 
                      className="w-full h-full object-cover" 
                    />
                    <Input 
                      value={parsedRecipe.recipe.imageUrl} 
                      onChange={(e) => setParsedRecipe({
                        ...parsedRecipe,
                        recipe: {
                          ...parsedRecipe.recipe,
                          imageUrl: e.target.value
                        }
                      })}
                      className="mt-2"
                      placeholder="Image URL"
                    />
                  </div>
                ) : (
                  <div className="h-48 rounded border border-neutral-200 flex items-center justify-center">
                    <div className="text-center">
                      <Link className="h-12 w-12 text-neutral-300 mx-auto mb-2" />
                      <p className="text-sm text-neutral-500">No image found</p>
                    </div>
                    <Input 
                      value={parsedRecipe.recipe.imageUrl || ''} 
                      onChange={(e) => setParsedRecipe({
                        ...parsedRecipe,
                        recipe: {
                          ...parsedRecipe.recipe,
                          imageUrl: e.target.value
                        }
                      })}
                      className="mt-2"
                      placeholder="Image URL"
                    />
                  </div>
                )}
              </div>
            </div>
            
            <div className="grid gap-6 md:grid-cols-2">
              <div>
                <h3 className="text-lg font-medium mb-2">Ingredients</h3>
                <div className="space-y-2 max-h-60 overflow-y-auto border rounded p-2">
                  {parsedRecipe.ingredients.map((ingredient: any, index: number) => (
                    <div key={index} className="grid grid-cols-4 gap-2">
                      <Input 
                        value={ingredient.quantity || ''} 
                        onChange={(e) => {
                          const newIngredients = [...parsedRecipe.ingredients];
                          newIngredients[index].quantity = e.target.value;
                          setParsedRecipe({
                            ...parsedRecipe,
                            ingredients: newIngredients
                          });
                        }}
                        placeholder="Qty"
                        className="col-span-1"
                      />
                      <Input 
                        value={ingredient.unit || ''} 
                        onChange={(e) => {
                          const newIngredients = [...parsedRecipe.ingredients];
                          newIngredients[index].unit = e.target.value;
                          setParsedRecipe({
                            ...parsedRecipe,
                            ingredients: newIngredients
                          });
                        }}
                        placeholder="Unit"
                        className="col-span-1"
                      />
                      <Input 
                        value={ingredient.name} 
                        onChange={(e) => {
                          const newIngredients = [...parsedRecipe.ingredients];
                          newIngredients[index].name = e.target.value;
                          setParsedRecipe({
                            ...parsedRecipe,
                            ingredients: newIngredients
                          });
                        }}
                        placeholder="Ingredient"
                        className="col-span-2"
                      />
                    </div>
                  ))}
                  <Button
                    variant="outline"
                    className="w-full mt-2"
                    onClick={() => {
                      setParsedRecipe({
                        ...parsedRecipe,
                        ingredients: [
                          ...parsedRecipe.ingredients,
                          { name: '', quantity: '', unit: '' }
                        ]
                      });
                    }}
                  >
                    Add Ingredient
                  </Button>
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">Instructions</h3>
                <div className="space-y-2 max-h-60 overflow-y-auto border rounded p-2">
                  {parsedRecipe.steps.map((step: any, index: number) => (
                    <div key={index} className="flex gap-2">
                      <div className="pt-2 min-w-8 text-center">{index + 1}.</div>
                      <Textarea 
                        value={step.instruction} 
                        onChange={(e) => {
                          const newSteps = [...parsedRecipe.steps];
                          newSteps[index].instruction = e.target.value;
                          setParsedRecipe({
                            ...parsedRecipe,
                            steps: newSteps
                          });
                        }}
                        placeholder={`Step ${index + 1}`}
                        rows={2}
                      />
                    </div>
                  ))}
                  <Button
                    variant="outline"
                    className="w-full mt-2"
                    onClick={() => {
                      setParsedRecipe({
                        ...parsedRecipe,
                        steps: [
                          ...parsedRecipe.steps,
                          { stepNumber: parsedRecipe.steps.length + 1, instruction: '' }
                        ]
                      });
                    }}
                  >
                    Add Step
                  </Button>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">Tags</h3>
              <div className="flex flex-wrap gap-2">
                {parsedRecipe.tags && parsedRecipe.tags.map((tag: string, index: number) => (
                  <Badge key={index} className="bg-neutral-100 text-neutral-800 hover:bg-neutral-200">
                    {tag}
                    <button 
                      className="ml-1 text-neutral-500 hover:text-neutral-800"
                      onClick={() => {
                        const newTags = parsedRecipe.tags.filter((_: any, i: number) => i !== index);
                        setParsedRecipe({
                          ...parsedRecipe,
                          tags: newTags
                        });
                      }}
                    >
                      ×
                    </button>
                  </Badge>
                ))}
                <Input 
                  placeholder="Add a tag and press Enter"
                  className="w-40"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                      e.preventDefault();
                      setParsedRecipe({
                        ...parsedRecipe,
                        tags: [...(parsedRecipe.tags || []), e.currentTarget.value.trim()]
                      });
                      e.currentTarget.value = '';
                    }
                  }}
                />
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">Notes</h3>
              <Textarea 
                value={parsedRecipe.recipe.notes || ''} 
                onChange={(e) => setParsedRecipe({
                  ...parsedRecipe,
                  recipe: {
                    ...parsedRecipe.recipe,
                    notes: e.target.value
                  }
                })}
                placeholder="Add any additional notes about this recipe"
                rows={3}
              />
            </div>
            
            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={() => setCurrentStep(0)}
              >
                Back
              </Button>
              <Button 
                onClick={() => setCurrentStep(2)}
                className="bg-primary hover:bg-primary-dark"
              >
                Continue
              </Button>
            </DialogFooter>
          </div>
        )}
        
        {/* Step 3: Save */}
        {currentStep === 2 && parsedRecipe && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Save Your Recipe</h3>
              
              <div className="bg-neutral-50 rounded-lg p-4 mb-4">
                <h4 className="font-medium">{parsedRecipe.recipe.title}</h4>
                <p className="text-sm text-neutral-600 mt-1">
                  {parsedRecipe.recipe.description || 'No description provided'}
                </p>
                <div className="flex items-center mt-2">
                  <span className="text-xs text-neutral-500">
                    {parsedRecipe.ingredients.length} ingredients • {parsedRecipe.steps.length} steps
                  </span>
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium">Add to Collection (Optional)</label>
                <Select
                  value={selectedFolder}
                  onValueChange={setSelectedFolder}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a collection" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    {folders && Array.isArray(folders) && folders.map((folder: Folder) => (
                      <SelectItem key={folder.id} value={folder.id.toString()}>
                        {folder.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={() => setCurrentStep(1)}
              >
                Back
              </Button>
              <Button 
                onClick={handleSaveRecipe}
                className="bg-primary hover:bg-primary-dark"
                disabled={saveRecipeMutation.isPending}
              >
                {saveRecipeMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  "Save Recipe"
                )}
              </Button>
            </DialogFooter>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
