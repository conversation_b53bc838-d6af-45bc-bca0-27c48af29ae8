import { useEffect, useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { queryClient } from "@/lib/queryClient";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Sheet,
  SheetContent
} from "@/components/ui/sheet";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { 
  Loader2, 
  Home, 
  Star, 
  Folder, 
  FolderPlus, 
  Plus,
  Trash2,
  MoreVertical
} from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { cn } from "@/lib/utils";

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
  selectedFolder?: number | null;
  onFolderSelect?: (folderId: number | null) => void;
  onCreateClick?: () => void;
}

const folderSchema = z.object({
  name: z.string().min(1, "Folder name is required").max(50, "Folder name is too long"),
});

type FolderFormValues = z.infer<typeof folderSchema>;

export default function Sidebar({ 
  isOpen, 
  onClose, 
  selectedFolder, 
  onFolderSelect,
  onCreateClick
}: SidebarProps) {
  const { toast } = useToast();
  const [createFolderOpen, setCreateFolderOpen] = useState(false);
  const [deleteFolderDialogOpen, setDeleteFolderDialogOpen] = useState(false);
  const [folderToDelete, setFolderToDelete] = useState<number | null>(null);
  
  // Fetch folders
  const { data: folders, isLoading: isFoldersLoading } = useQuery({
    queryKey: ['/api/folders'],
  });
  
  // Fetch recipes count
  const { data: recipes } = useQuery({
    queryKey: ['/api/recipes'],
  });
  
  // Fetch favorites count
  const { data: favorites } = useQuery({
    queryKey: ['/api/favorites'],
  });
  
  // Create folder form
  const form = useForm<FolderFormValues>({
    resolver: zodResolver(folderSchema),
    defaultValues: {
      name: "",
    },
  });
  
  // Create folder mutation
  const createFolderMutation = useMutation({
    mutationFn: async (values: FolderFormValues) => {
      const res = await apiRequest("POST", "/api/folders", values);
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/folders'] });
      setCreateFolderOpen(false);
      form.reset();
      toast({
        title: "Folder created",
        description: "Your new folder has been created successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to create folder",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  const onSubmit = (values: FolderFormValues) => {
    createFolderMutation.mutate(values);
  };
  
  // Delete folder mutation
  const deleteFolderMutation = useMutation({
    mutationFn: async (folderId: number) => {
      await apiRequest("DELETE", `/api/folders/${folderId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/folders'] });
      
      // If the deleted folder was selected, reset to "All Recipes"
      if (selectedFolder === folderToDelete && onFolderSelect) {
        onFolderSelect(null);
      }
      
      setDeleteFolderDialogOpen(false);
      setFolderToDelete(null);
      
      toast({
        title: "Collection deleted",
        description: "The collection has been deleted successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to delete collection",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Handle delete folder
  const handleDeleteFolder = () => {
    if (folderToDelete) {
      deleteFolderMutation.mutate(folderToDelete);
    }
  };
  
  // Handle folder selection
  const handleFolderSelect = (folderId: number | null) => {
    if (onFolderSelect) {
      onFolderSelect(folderId);
      if (window.innerWidth < 768) {
        onClose();
      }
    }
  };
  
  // Content for sidebar (both mobile and desktop)
  const sidebarContent = (
    <div className="h-full flex flex-col">
      <div className="p-4 flex-1 overflow-y-auto">
        <h2 className="text-lg font-heading font-semibold text-neutral-800 mb-4">My Collections</h2>
        
        {/* Folders/Collections */}
        <div className="space-y-1">
          <Button
            variant="ghost"
            className={cn(
              "w-full justify-start text-sm font-medium",
              selectedFolder === null ? "bg-primary/10 text-primary" : "text-neutral-700 hover:bg-neutral-100"
            )}
            onClick={() => handleFolderSelect(null)}
          >
            <Home className="mr-3 h-5 w-5" />
            All Recipes
            <span className="ml-auto bg-primary/20 rounded-full px-2 text-xs text-primary font-semibold">
              {recipes?.length || 0}
            </span>
          </Button>
          
          <Button
            variant="ghost"
            className={cn(
              "w-full justify-start text-sm font-medium",
              selectedFolder === -1 ? "bg-primary/10 text-primary" : "text-neutral-700 hover:bg-neutral-100"
            )}
            onClick={() => handleFolderSelect(-1)}
          >
            <Star className="mr-3 h-5 w-5" />
            Favorites
            <span className="ml-auto bg-neutral-200 rounded-full px-2 text-xs text-neutral-600 font-semibold">
              {favorites?.length || 0}
            </span>
          </Button>
          
          {isFoldersLoading ? (
            <div className="py-4 flex justify-center">
              <Loader2 className="h-5 w-5 animate-spin text-primary" />
            </div>
          ) : (
            <>
              {folders?.map((folder) => (
                <div key={folder.id} className="relative group flex items-center">
                  <Button
                    variant="ghost"
                    className={cn(
                      "w-full justify-start text-sm font-medium",
                      selectedFolder === folder.id ? "bg-primary/10 text-primary" : "text-neutral-700 hover:bg-neutral-100"
                    )}
                    onClick={() => handleFolderSelect(folder.id)}
                  >
                    <Folder className="mr-3 h-5 w-5" />
                    {folder.name}
                    <span className="ml-auto mr-6 bg-neutral-200 rounded-full px-2 text-xs text-neutral-600 font-semibold">
                      {folder.recipeCount || 0}
                    </span>
                  </Button>
                  
                  <div className="absolute right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem 
                          className="text-red-500 cursor-pointer"
                          onClick={(e) => {
                            e.stopPropagation();
                            setFolderToDelete(folder.id);
                            setDeleteFolderDialogOpen(true);
                          }}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              ))}
            </>
          )}
          
          {/* Add new folder button */}
          <Dialog open={createFolderOpen} onOpenChange={setCreateFolderOpen}>
            <DialogTrigger asChild>
              <Button variant="ghost" className="w-full justify-start text-sm font-medium text-neutral-600 hover:bg-neutral-100">
                <FolderPlus className="mr-3 h-5 w-5" />
                Add New Collection
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Collection</DialogTitle>
              </DialogHeader>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Collection Name</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., Dinner Ideas, Quick Meals" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <DialogFooter>
                    <Button
                      type="submit"
                      className="bg-primary hover:bg-primary-dark"
                      disabled={createFolderMutation.isPending}
                    >
                      {createFolderMutation.isPending && (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      )}
                      Create Collection
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>
      </div>
      
      <div className="border-t border-neutral-200 p-4">
        <Button
          className="w-full bg-primary hover:bg-primary-dark"
          onClick={onCreateClick}
        >
          <Plus className="mr-3 h-5 w-5" />
          Create New Recipe
        </Button>
      </div>
    </div>
  );
  
  // Get folder name for confirmation dialog
  const folderToDeleteName = folders?.find(f => f.id === folderToDelete)?.name || "this collection";
  
  return (
    <>
      {/* Desktop sidebar */}
      <aside className="hidden md:flex flex-col w-64 bg-white border-r border-neutral-200 overflow-y-auto">
        {sidebarContent}
      </aside>
      
      {/* Mobile sidebar */}
      <Sheet open={isOpen} onOpenChange={onClose}>
        <SheetContent side="left" className="p-0 w-72 sm:max-w-sm">
          {sidebarContent}
        </SheetContent>
      </Sheet>
      
      {/* Delete folder confirmation dialog */}
      <AlertDialog open={deleteFolderDialogOpen} onOpenChange={setDeleteFolderDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the collection "{folderToDeleteName}" and remove all recipes from it.
              The recipes themselves will not be deleted.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteFolder}
              className="bg-red-500 hover:bg-red-600 text-white"
            >
              {deleteFolderMutation.isPending ? (
                <Loader2 className="h-4 w-4 mr-1 animate-spin" />
              ) : null}
              Delete Collection
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
