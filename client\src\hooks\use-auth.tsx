import { createContext, ReactNode, useContext } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { User as SelectUser, InsertUser } from '@shared/schema';
import { apiRequest, getQueryFn, queryClient } from '../lib/queryClient';
import { useToast } from '@/hooks/use-toast';

// Simple types for auth data
type LoginData = {
  username: string;
  password: string;
};

// Auth context interface
interface AuthContextInterface {
  user: SelectUser | null;
  isLoading: boolean;
  error: Error | null;
  login: (data: LoginData) => void;
  register: (data: InsertUser) => void;
  logout: () => void;
  demoLogin: () => void;
  isLoginPending: boolean;
  isRegisterPending: boolean;
  isLogoutPending: boolean;
  isDemoLoginPending: boolean;
}

// Create the auth context with default values
const AuthContext = createContext<AuthContextInterface>({
  user: null,
  isLoading: false,
  error: null,
  login: () => {},
  register: () => {},
  logout: () => {},
  demoLogin: () => {},
  isLoginPending: false,
  isRegisterPending: false,
  isLogoutPending: false,
  isDemoLoginPending: false,
});

// Auth provider component
export function AuthProvider({ children }: { children: ReactNode }) {
  const { toast } = useToast();

  // Fetch current user
  const {
    data: user,
    error,
    isLoading,
  } = useQuery<SelectUser | null, Error>({
    queryKey: ['/api/user'],
    queryFn: getQueryFn({ on401: 'returnNull' }),
  });

  // Login mutation
  const loginMutation = useMutation({
    mutationFn: async (credentials: LoginData) => {
      const res = await apiRequest('POST', '/api/login', credentials);
      return await res.json();
    },
    onSuccess: (userData: SelectUser) => {
      queryClient.setQueryData(['/api/user'], userData);
      toast({
        title: 'Login successful',
        description: `Welcome back, ${userData.username}!`,
      });
    },
    onError: (err: Error) => {
      toast({
        title: 'Login failed',
        description: err.message,
        variant: 'destructive',
      });
    },
  });

  // Register mutation
  const registerMutation = useMutation({
    mutationFn: async (userData: InsertUser) => {
      const res = await apiRequest('POST', '/api/register', userData);
      return await res.json();
    },
    onSuccess: (userData: SelectUser) => {
      queryClient.setQueryData(['/api/user'], userData);
      toast({
        title: 'Registration successful',
        description: `Welcome to Recipe Safe, ${userData.username}!`,
      });
    },
    onError: (err: Error) => {
      toast({
        title: 'Registration failed',
        description: err.message,
        variant: 'destructive',
      });
    },
  });

  // Logout mutation
  const logoutMutation = useMutation({
    mutationFn: async () => {
      await apiRequest('POST', '/api/logout');
    },
    onSuccess: () => {
      queryClient.setQueryData(['/api/user'], null);
      toast({
        title: 'Logged out',
        description: 'You have been logged out successfully.',
      });
    },
    onError: (err: Error) => {
      toast({
        title: 'Logout failed',
        description: err.message,
        variant: 'destructive',
      });
    },
  });

  // Demo login mutation
  const demoLoginMutation = useMutation({
    mutationFn: async () => {
      const res = await apiRequest('POST', '/api/demo-login');
      return await res.json();
    },
    onSuccess: (userData: SelectUser) => {
      queryClient.setQueryData(['/api/user'], userData);
      toast({
        title: 'Demo Login Successful',
        description: `Welcome to Recipe Safe! You're logged in as ${userData.username}.`,
      });
    },
    onError: (err: Error) => {
      toast({
        title: 'Demo Login Failed',
        description: err.message,
        variant: 'destructive',
      });
    },
  });

  // Create simplified interface functions
  const login = (data: LoginData) => {
    console.log("Login function called with:", data.username);
    loginMutation.mutate(data);
  };

  const register = (data: InsertUser) => {
    console.log("Register function called with:", data.username);
    registerMutation.mutate(data);
  };

  const logout = () => {
    console.log("Logout function called");
    logoutMutation.mutate();
  };

  const demoLogin = () => {
    console.log("Demo login function called");
    demoLoginMutation.mutate();
  };

  // Provide the auth context
  return (
    <AuthContext.Provider
      value={{
        user: user || null,
        isLoading,
        error,
        login,
        register,
        logout,
        demoLogin,
        isLoginPending: loginMutation.isPending,
        isRegisterPending: registerMutation.isPending,
        isLogoutPending: logoutMutation.isPending,
        isDemoLoginPending: demoLoginMutation.isPending,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook for using auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Export the context for testing
export { AuthContext };
