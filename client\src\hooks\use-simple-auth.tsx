import { createContext, ReactNode, useContext, useEffect, useState } from "react";
import { useToast } from "@/hooks/use-toast";

interface SimpleAuthContextType {
  isAuthenticated: boolean;
  login: (password: string) => boolean;
  logout: () => void;
  isLoading: boolean;
}

const CORRECT_PASSWORD = "happy days";
const LOCAL_STORAGE_KEY = "recipeApp.isAuthenticated";

export const SimpleAuthContext = createContext<SimpleAuthContextType | null>(null);

export function SimpleAuthProvider({ children }: { children: ReactNode }) {
  const { toast } = useToast();
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Check local storage on initial load
  useEffect(() => {
    const storedAuth = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (storedAuth === "true") {
      setIsAuthenticated(true);
    }
    setIsLoading(false);
  }, []);

  const login = (password: string): boolean => {
    console.log("Simple login called with password");
    
    if (password === CORRECT_PASSWORD) {
      console.log("Password correct, authenticating");
      setIsAuthenticated(true);
      localStorage.setItem(LOCAL_STORAGE_KEY, "true");
      toast({
        title: "Welcome to Recipe Safe",
        description: "You are now logged in as the Family user",
      });
      return true;
    } else {
      console.log("Password incorrect");
      toast({
        title: "Authentication failed",
        description: "Incorrect password",
        variant: "destructive",
      });
      return false;
    }
  };

  const logout = () => {
    console.log("Logging out");
    setIsAuthenticated(false);
    localStorage.removeItem(LOCAL_STORAGE_KEY);
    toast({
      title: "Logged out",
      description: "You have been logged out",
    });
  };

  return (
    <SimpleAuthContext.Provider
      value={{
        isAuthenticated,
        login,
        logout,
        isLoading,
      }}
    >
      {children}
    </SimpleAuthContext.Provider>
  );
}

export function useSimpleAuth() {
  const context = useContext(SimpleAuthContext);
  if (!context) {
    throw new Error("useSimpleAuth must be used within a SimpleAuthProvider");
  }
  return context;
}