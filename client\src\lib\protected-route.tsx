import { useAuth } from "@/hooks/use-auth";
import { Loader2 } from "lucide-react";
import { Redirect, Route } from "wouter";

export function ProtectedRoute({
  path,
  component: Component,
}: {
  path: string;
  component: () => React.JSX.Element;
}) {
  // Try-catch around useAuth to handle potential errors
  let auth;
  try {
    auth = useAuth();
  } catch (error) {
    console.error("Error using auth context in ProtectedRoute:", error);
    // Return a redirect to auth page if there's an error
    return (
      <Route path={path}>
        <Redirect to="/auth" />
      </Route>
    );
  }
  
  const { user, isLoading } = auth;

  if (isLoading) {
    return (
      <Route path={path}>
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </Route>
    );
  }

  if (!user) {
    return (
      <Route path={path}>
        <Redirect to="/auth" />
      </Route>
    );
  }

  return <Route path={path} component={Component} />;
}
