import { useSimpleAuth } from "@/hooks/use-simple-auth";
import { Loader2 } from "lucide-react";
import { Redirect, Route } from "wouter";

export function SimpleProtectedRoute({
  path,
  component: Component,
}: {
  path: string;
  component: () => React.JSX.Element;
}) {
  const { isAuthenticated, isLoading } = useSimpleAuth();

  if (isLoading) {
    return (
      <Route path={path}>
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="h-8 w-8 animate-spin text-border" />
        </div>
      </Route>
    );
  }

  if (!isAuthenticated) {
    return (
      <Route path={path}>
        <Redirect to="/password" />
      </Route>
    );
  }

  return <Route path={path} component={Component} />;
}