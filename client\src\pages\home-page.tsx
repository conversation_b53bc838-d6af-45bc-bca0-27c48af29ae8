import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import Layout from "@/components/Layout";
import RecipeCard from "@/components/RecipeCard";
import RecipeImportModal from "@/components/RecipeImportModal";
import CreateRecipeModal from "@/components/CreateRecipeModal";
import { Button } from "@/components/ui/button";
import { Loader2, Grid, List, Filter, SortDesc } from "lucide-react";
import { useAuth } from "@/hooks/use-auth";
import { queryClient } from "@/lib/queryClient";

export default function HomePage() {
  const { user } = useAuth();
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showImportModal, setShowImportModal] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedFolder, setSelectedFolder] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>("");
  
  // Set demo mode for direct access
  useEffect(() => {
    // Try to get demo user from localStorage
    try {
      const savedUser = localStorage.getItem('demo_user');
      if (savedUser && !user) {
        const parsedUser = JSON.parse(savedUser);
        console.log("Homepage using saved demo user:", parsedUser);
        queryClient.setQueryData(['/api/user'], parsedUser);
        localStorage.setItem('demo_mode', 'true');
      }
    } catch (err) {
      console.error("Error parsing saved user:", err);
    }
  }, [user]);
  
  // Fetch recipes - always enabled for demo
  const { data: recipes = [], isLoading: isLoadingRecipes, refetch: refetchRecipes } = useQuery<any[]>({
    queryKey: ["/api/recipes"],
    enabled: true,
    staleTime: 0, // Always consider the data stale to force refetch
    refetchOnMount: true, // Refetch on component mount
    refetchOnWindowFocus: true, // Refetch when window gains focus
  });
  
  // Fetch folders - always enabled for demo
  const { data: folders = [], isLoading: isLoadingFolders } = useQuery<any[]>({
    queryKey: ["/api/folders"],
    enabled: true,
  });
  
  // Fetch favorites - always enabled for demo
  const { data: favorites = [], isLoading: isLoadingFavorites } = useQuery<any[]>({
    queryKey: ["/api/favorites"],
    enabled: true,
  });
  
  // Force refetch recipes on component mount
  useEffect(() => {
    // Small delay to ensure the refetch happens after initial render
    const timer = setTimeout(() => {
      console.log("Forcing recipes refetch");
      refetchRecipes();
    }, 500);
    
    return () => clearTimeout(timer);
  }, [refetchRecipes]);
  
  // Filter recipes if folder is selected or search query is provided
  let filteredRecipes = recipes || [];
  
  if (selectedFolder === null) {
    // Show all recipes when "All Recipes" is selected
    filteredRecipes = recipes;
  } else if (selectedFolder === -1) {
    // Show favorites
    filteredRecipes = favorites;
  } else if (selectedFolder) {
    // Filter by folder ID - This would normally call an API endpoint
    // But for now, just simulate it for the demo
    const folderRecipes = recipes.filter(recipe => recipe.folderId === selectedFolder);
    filteredRecipes = folderRecipes;
  }
  
  // Handle search functionality
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };
  
  const handleFolderSelect = (folderId: number | null) => {
    setSelectedFolder(folderId);
  };
  
  // Show appropriate loading state when fetching data
  if (isLoadingRecipes || isLoadingFolders || isLoadingFavorites) {
    return (
      <Layout onSearch={handleSearch} onImportClick={() => setShowImportModal(true)}>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2 text-neutral-600">Loading your recipes...</span>
        </div>
      </Layout>
    );
  }
  
  // Apply search filter if query exists
  if (searchQuery && searchQuery.trim().length > 0) {
    filteredRecipes = filteredRecipes.filter(recipe => 
      recipe.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (recipe.description && recipe.description.toLowerCase().includes(searchQuery.toLowerCase()))
    );
  }
  
  // Show empty state if no recipes
  if (filteredRecipes && filteredRecipes.length === 0) {
    return (
      <Layout onSearch={handleSearch} onImportClick={() => setShowImportModal(true)}>
        <div className="bg-white rounded-lg border border-neutral-200 p-8 text-center">
          <svg className="h-16 w-16 text-neutral-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z"></path>
          </svg>
          <h3 className="text-lg font-heading font-medium text-neutral-900 mb-2">No recipes found</h3>
          <p className="text-neutral-600 mb-4">
            {recipes && recipes.length > 0 
              ? (searchQuery 
                ? `No recipes match your search for "${searchQuery}"`
                : selectedFolder !== null 
                  ? "No recipes in this collection" 
                  : "No recipes found with the current filters")
              : "You haven't added any recipes to your collection yet."
            }
          </p>
          <div className="flex justify-center gap-3">
            <Button className="bg-primary hover:bg-primary-dark" onClick={() => setShowCreateModal(true)}>
              Create New Recipe
            </Button>
            <Button variant="outline" className="text-primary border-primary hover:bg-neutral-50" onClick={() => setShowImportModal(true)}>
              Import from URL
            </Button>
          </div>
        </div>
        
        {/* Modals */}
        <RecipeImportModal 
          isOpen={showImportModal} 
          onClose={() => setShowImportModal(false)}
        />
        <CreateRecipeModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
        />
      </Layout>
    );
  }
  
  return (
    <Layout 
      selectedFolder={selectedFolder} 
      onFolderSelect={handleFolderSelect}
      onSearch={handleSearch}
      onImportClick={() => setShowImportModal(true)}
      onCreateClick={() => setShowCreateModal(true)}
    >
      {/* Recipe List Header */}
      <div className="mb-6 flex justify-between items-center">
        <h1 className="text-2xl font-heading font-bold text-neutral-900">
          {selectedFolder === null ? "All Recipes" : folders?.find(f => f.id === selectedFolder)?.name || "Recipes"}
        </h1>
        
        <div className="flex items-center space-x-2">
          {/* View toggle */}
          <div className="bg-white rounded-lg flex shadow-sm">
            <Button
              variant={viewMode === "grid" ? "default" : "ghost"}
              size="icon"
              aria-label="Grid view"
              className={viewMode === "grid" ? "text-primary border border-primary rounded-l-lg" : "rounded-l-lg"}
              onClick={() => setViewMode("grid")}
            >
              <Grid className="h-5 w-5" />
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "ghost"}
              size="icon"
              aria-label="List view"
              className={viewMode === "list" ? "text-primary border border-primary rounded-r-lg" : "rounded-r-lg"}
              onClick={() => setViewMode("list")}
            >
              <List className="h-5 w-5" />
            </Button>
          </div>
          
          {/* Filter dropdown */}
          <Button variant="outline" size="sm" className="flex items-center">
            <Filter className="h-4 w-4 mr-1" />
            <span>Filter</span>
          </Button>
          
          {/* Sort dropdown */}
          <Button variant="outline" size="sm" className="flex items-center">
            <SortDesc className="h-4 w-4 mr-1" />
            <span>Sort: Newest</span>
          </Button>
        </div>
      </div>
      
      {/* Recipe Grid/List */}
      <div className={viewMode === "grid" 
        ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" 
        : "space-y-4"
      }>
        {filteredRecipes && filteredRecipes.map((recipe) => (
          <RecipeCard 
            key={recipe.id} 
            recipe={recipe} 
            viewMode={viewMode}
          />
        ))}
      </div>
      
      {/* Pagination */}
      {filteredRecipes && filteredRecipes.length > 12 && (
        <div className="mt-8 flex justify-center">
          <nav className="inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
            <Button variant="outline" size="icon" className="rounded-l-md">
              <span className="sr-only">Previous</span>
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            </Button>
            <Button variant="default" className="bg-primary text-white">1</Button>
            <Button variant="outline">2</Button>
            <Button variant="outline">3</Button>
            <span className="relative inline-flex items-center px-4 py-2 border border-neutral-300 bg-white text-sm font-medium text-neutral-700">
              ...
            </span>
            <Button variant="outline">6</Button>
            <Button variant="outline" size="icon" className="rounded-r-md">
              <span className="sr-only">Next</span>
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </Button>
          </nav>
        </div>
      )}
      
      {/* Modals */}
      <RecipeImportModal 
        isOpen={showImportModal} 
        onClose={() => setShowImportModal(false)}
      />
      <CreateRecipeModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
      />
    </Layout>
  );
}
