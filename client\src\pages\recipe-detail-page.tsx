import { useEffect, useState } from "react";
import { useParams, useLocation } from "wouter";
import { useQuery, useMutation } from "@tanstack/react-query";
import { queryClient } from "@/lib/queryClient";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog";
import { Loader2, Heart, Share2, Printer, Clock, GitFork, ArrowLeft, Trash2 } from "lucide-react";
import RecipeDetailModal from "@/components/RecipeDetailModal";
import AddToCollectionDialog from "@/components/AddToCollectionDialog";
// Import types
import { 
  Recipe, 
  Ingredient, 
  Step,
  Tag,
} from "@shared/schema";

// Define API response type
interface RecipeDetailResponse {
  recipe: Recipe;
  ingredients: Ingredient[];
  steps: Step[];
  tags: Tag[];
  isFavorite: boolean;
}

export default function RecipeDetailPage() {
  const { id } = useParams();
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [addToCollectionOpen, setAddToCollectionOpen] = useState(false);
  const [selectedFolder, setSelectedFolder] = useState<number | null>(null);
  
  // Fetch recipe details
  const { data, isLoading, error } = useQuery<RecipeDetailResponse>({
    queryKey: ['/api/recipes', id],
    enabled: !!id,
    retry: 3, // Retry failed requests up to 3 times
  });
  
  // Toggle favorite mutation
  const toggleFavoriteMutation = useMutation({
    mutationFn: async () => {
      if (!id) return;
      
      const recipeData = data as RecipeDetailResponse;
      const isFavorite = recipeData?.isFavorite;
      
      if (isFavorite) {
        await apiRequest("DELETE", `/api/favorites/${id}`);
      } else {
        await apiRequest("POST", `/api/favorites/${id}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/recipes', id] });
      queryClient.invalidateQueries({ queryKey: ['/api/favorites'] });
      
      const recipeData = data as RecipeDetailResponse;
      
      toast({
        title: recipeData?.isFavorite ? "Removed from favorites" : "Added to favorites",
        description: recipeData?.isFavorite 
          ? "Recipe has been removed from your favorites" 
          : "Recipe has been added to your favorites",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Handle back navigation
  const handleBack = () => {
    navigate("/");
  };
  
  // Handle folder selection from sidebar
  const handleFolderSelect = (folderId: number | null) => {
    setSelectedFolder(folderId);
    navigate("/"); // Go back to home with the selected folder
  };
  
  // Delete recipe mutation
  const deleteRecipeMutation = useMutation({
    mutationFn: async () => {
      if (!id) return;
      await apiRequest("DELETE", `/api/recipes/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/recipes'] });
      queryClient.invalidateQueries({ queryKey: ['/api/favorites'] });
      
      toast({
        title: "Recipe deleted",
        description: "Recipe has been permanently deleted",
      });
      
      // Navigate back to home page
      navigate("/");
    },
    onError: (error: Error) => {
      toast({
        title: "Error deleting recipe",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Print recipe
  const handlePrint = () => {
    window.print();
  };
  
  if (isLoading) {
    return (
      <Layout onFolderSelect={handleFolderSelect} selectedFolder={selectedFolder}>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2 text-neutral-600">Loading recipe details...</span>
        </div>
      </Layout>
    );
  }
  
  if (error || !data) {
    return (
      <Layout onFolderSelect={handleFolderSelect} selectedFolder={selectedFolder}>
        <div className="bg-white rounded-lg border border-neutral-200 p-8 text-center">
          <h3 className="text-lg font-heading font-medium text-neutral-900 mb-2">Recipe not found</h3>
          <p className="text-neutral-600 mb-4">
            The recipe you're looking for doesn't exist or you don't have permission to view it.
          </p>
          <Button onClick={handleBack}>Go back to recipes</Button>
        </div>
      </Layout>
    );
  }
  
  // Make sure all required data is present
  const recipeData = data as RecipeDetailResponse;
  if (!recipeData || !recipeData.recipe) {
    return (
      <Layout onFolderSelect={handleFolderSelect} selectedFolder={selectedFolder}>
        <div className="bg-white rounded-lg border border-neutral-200 p-8 text-center">
          <h3 className="text-lg font-heading font-medium text-neutral-900 mb-2">Recipe data is incomplete</h3>
          <p className="text-neutral-600 mb-4">
            We couldn't load all the recipe details. Please go back and try again.
          </p>
          <Button onClick={handleBack}>Go back to recipes</Button>
        </div>
      </Layout>
    );
  }
  
  // Extract data safely
  const { recipe, ingredients = [], steps = [], tags = [], isFavorite = false } = recipeData;
  
  return (
    <Layout onFolderSelect={handleFolderSelect} selectedFolder={selectedFolder}>
      <div className="pb-8">
        {/* Back button */}
        <Button
          variant="ghost"
          className="mb-4"
          onClick={handleBack}
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to recipes
        </Button>
        
        <div className="relative">
          <div className="h-64 w-full bg-gradient-to-b from-neutral-800 to-neutral-900 rounded-t-lg overflow-hidden">
            {recipe.imageUrl ? (
              <img 
                src={recipe.imageUrl} 
                alt={recipe.title} 
                className="w-full h-full object-cover opacity-80" 
              />
            ) : (
              <div className="flex items-center justify-center h-full">
                <GitFork className="h-16 w-16 text-white/30" />
              </div>
            )}
          </div>
          
          <div className="absolute bottom-0 left-0 right-0 p-6 bg-gradient-to-b from-transparent to-neutral-900">
            <h1 className="text-3xl font-heading font-bold text-white">{recipe.title}</h1>
            <div className="flex items-center mt-2">
              <div className="flex items-center text-white/90 text-sm">
                <Clock className="h-4 w-4 mr-1" />
                <span>{recipe.prepTime ? `${recipe.prepTime} prep` : "Prep time not specified"}</span>
                {recipe.cookTime && (
                  <>
                    <span className="mx-2">•</span>
                    <span>{recipe.cookTime} cook</span>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-b-lg shadow-sm">
          {/* Tags */}
          <div className="flex flex-wrap gap-2 mb-4">
            {tags && tags.map((tag: Tag, index: number) => (
              <span key={index} className="px-3 py-1 rounded-full bg-neutral-100 text-neutral-700 text-sm font-medium">
                {tag.name}
              </span>
            ))}
          </div>
          
          {/* Description */}
          {recipe.description && (
            <p className="text-neutral-700 mb-6">{recipe.description}</p>
          )}
          
          {/* Action buttons */}
          <div className="flex items-center space-x-4 mb-6">
            <Button
              variant="ghost"
              className={isFavorite ? "text-primary" : "text-neutral-600 hover:text-primary"}
              onClick={() => toggleFavoriteMutation.mutate()}
              disabled={toggleFavoriteMutation.isPending}
            >
              {toggleFavoriteMutation.isPending ? (
                <Loader2 className="h-5 w-5 mr-1 animate-spin" />
              ) : (
                <Heart className={`h-5 w-5 mr-1 ${isFavorite ? "fill-primary" : ""}`} />
              )}
              {isFavorite ? "Saved to Favorites" : "Save to Favorites"}
            </Button>
            
            <Button variant="ghost" className="text-neutral-600 hover:text-primary">
              <Share2 className="h-5 w-5 mr-1" />
              Share Recipe
            </Button>
            
            <Button variant="ghost" className="text-neutral-600 hover:text-primary" onClick={handlePrint}>
              <Printer className="h-5 w-5 mr-1" />
              Print
            </Button>
          </div>
          
          <div className="md:flex md:space-x-8">
            {/* Ingredients Column */}
            <div className="md:w-1/3 mb-6 md:mb-0">
              <h2 className="text-xl font-heading font-semibold text-neutral-900 mb-3">Ingredients</h2>
              <ul className="space-y-3 font-recipe">
                {ingredients && ingredients.map((ingredient: Ingredient, index: number) => (
                  <li key={index} className="flex items-start">
                    <span className="text-neutral-700">
                      {ingredient.quantity && ingredient.unit 
                        ? `${ingredient.quantity} ${ingredient.unit} ${ingredient.name}`
                        : ingredient.quantity 
                          ? `${ingredient.quantity} ${ingredient.name}`
                          : ingredient.name
                      }
                    </span>
                  </li>
                ))}
              </ul>
            </div>
            
            {/* Instructions Column */}
            <div className="md:w-2/3">
              <h2 className="text-xl font-heading font-semibold text-neutral-900 mb-3">Instructions</h2>
              <ol className="space-y-4 list-decimal list-outside pl-5 font-recipe">
                {steps && steps.map((step: Step, index: number) => (
                  <li key={index} className="text-neutral-700">
                    {step.instruction}
                  </li>
                ))}
              </ol>
              
              {/* Notes */}
              {recipe.notes && (
                <div className="mt-8 pt-6 border-t border-neutral-200">
                  <h3 className="text-lg font-heading font-semibold text-neutral-900 mb-2">Notes</h3>
                  <div className="bg-neutral-50 rounded-lg p-4 text-neutral-700 font-recipe">
                    <p>{recipe.notes}</p>
                  </div>
                </div>
              )}
              
              {/* Source */}
              {recipe.sourceUrl && (
                <div className="mt-8 pt-6 border-t border-neutral-200">
                  <h3 className="text-lg font-heading font-semibold text-neutral-900 mb-2">Source</h3>
                  <div className="text-sm text-neutral-600">
                    <p>Imported from: <a href={recipe.sourceUrl} className="text-primary hover:underline" target="_blank" rel="noopener noreferrer">{recipe.sourceUrl}</a></p>
                    <p>Added to Recipe Safe on {new Date(recipe.createdAt).toLocaleDateString()}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
          
          {/* Actions */}
          <div className="mt-8 pt-6 border-t border-neutral-200 flex justify-between">
            <div className="flex space-x-2">
              <Button variant="outline" onClick={() => setEditModalOpen(true)}>
                Edit Recipe
              </Button>
              <Button 
                variant="outline" 
                className="text-red-500 hover:text-red-700 hover:bg-red-50"
                onClick={() => setDeleteDialogOpen(true)}
                disabled={deleteRecipeMutation.isPending}
              >
                {deleteRecipeMutation.isPending ? (
                  <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                ) : (
                  <Trash2 className="h-4 w-4 mr-1" />
                )}
                Delete
              </Button>
            </div>
            <div>
              <Button 
                variant="outline" 
                className="mr-2"
                onClick={() => setAddToCollectionOpen(true)}
              >
                Add to Collection
              </Button>
            </div>
          </div>
        </div>
      </div>
      
      {/* Edit Recipe Modal */}
      <RecipeDetailModal
        isOpen={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        recipeId={parseInt(id || '0')}
        isEditing={true}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the recipe 
              "{recipe?.title}" and all of its data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deleteRecipeMutation.mutate()}
              className="bg-red-500 hover:bg-red-600 text-white"
            >
              {deleteRecipeMutation.isPending ? (
                <Loader2 className="h-4 w-4 mr-1 animate-spin" />
              ) : null}
              Delete Recipe
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      {/* Add to Collection Dialog */}
      <AddToCollectionDialog
        isOpen={addToCollectionOpen}
        onClose={() => setAddToCollectionOpen(false)}
        recipeId={parseInt(id || '0')}
      />
    </Layout>
  );
}
