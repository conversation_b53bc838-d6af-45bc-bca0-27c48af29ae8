import { useState, useEffect } from "react";
import { Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { queryClient } from "@/lib/queryClient";

export default function TestHomePage() {
  const [user, setUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // Directly fetch the user data without using context
  useEffect(() => {
    const fetchUser = async () => {
      try {
        setIsLoading(true);
        console.log("Directly fetching user data from /api/user...");
        
        // First check if we have a saved user
        const savedUser = localStorage.getItem('demo_user');
        if (savedUser) {
          try {
            const parsedUser = JSON.parse(savedUser);
            console.log("Using saved demo user from localStorage:", parsedUser);
            setUser(parsedUser);
            queryClient.setQueryData(['/api/user'], parsedUser);
            
            // Don't show toast to avoid multiple notifications
            console.log("Set user from localStorage");
            setIsLoading(false);
            return; // Exit early - don't make the API call
          } catch (err) {
            console.error("Error parsing saved user:", err);
          }
        }
        
        // If no saved user, try the API
        const response = await fetch('/api/user', {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        });
        
        console.log("User API response status:", response.status);
        
        if (response.ok) {
          const userData = await response.json();
          console.log("User data fetched successfully:", userData);
          setUser(userData);
          queryClient.setQueryData(['/api/user'], userData);
          localStorage.setItem('demo_user', JSON.stringify(userData));
          
          // Show success message
          toast({
            title: 'Authentication Successful',
            description: `You're logged in as ${userData.username}.`
          });
        } else {
          console.log("Failed to fetch user data");
          setError("Not authenticated");
        }
      } catch (err) {
        console.error("Error fetching user:", err);
        setError("Error fetching user data");
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchUser();
  }, [toast]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="ml-2">Loading user data directly...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">Demo Home Page</h1>
      
      {user ? (
        <div className="bg-green-100 border border-green-300 rounded-lg p-4 mb-6">
          <h2 className="text-xl font-semibold text-green-800">Logged in successfully!</h2>
          <p className="text-green-700">
            Welcome, <strong>{user.username}</strong>! You are logged in with the demo account.
          </p>
          <div className="mt-4 p-4 bg-white rounded border border-green-200">
            <h3 className="text-lg font-medium mb-2">User information:</h3>
            <pre className="bg-neutral-50 p-3 rounded overflow-auto text-sm">
              {JSON.stringify(user, null, 2)}
            </pre>
          </div>
        </div>
      ) : (
        <div className="bg-red-100 border border-red-300 rounded-lg p-4 mb-6">
          <h2 className="text-xl font-semibold text-red-800">Not logged in</h2>
          <p className="text-red-700">
            You are not currently logged in. Please try the demo login again.
          </p>
        </div>
      )}
      
      <div className="mt-6 flex flex-col sm:flex-row gap-4">
        {!user ? (
          <button 
            onClick={async () => {
              try {
                console.log("Triggering demo login directly from test page");
                const response = await fetch('/api/demo-login', {
                  method: 'POST',
                  credentials: 'include',
                  headers: {
                    'Content-Type': 'application/json'
                  }
                });
                
                if (response.ok) {
                  const userData = await response.json();
                  console.log("Demo login successful:", userData);
                  setUser(userData);
                  queryClient.setQueryData(['/api/user'], userData);
                  toast({
                    title: 'Demo Login Successful',
                    description: `Logged in as ${userData.username}`,
                  });
                  localStorage.setItem('demo_mode', 'true');
                  localStorage.setItem('demo_user', JSON.stringify(userData));
                } else {
                  console.error("Demo login failed with status:", response.status);
                  toast({
                    title: 'Login Failed',
                    description: 'Failed to log in with demo account.',
                    variant: 'destructive'
                  });
                }
              } catch (err) {
                console.error("Error during direct demo login:", err);
                toast({
                  title: 'Login Error',
                  description: 'An error occurred during login.',
                  variant: 'destructive'
                });
              }
            }}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Try Direct Demo Login
          </button>
        ) : (
          <button
            onClick={() => {
              // Force the demo mode flag to be set
              localStorage.setItem('demo_mode', 'true');
              localStorage.setItem('demo_user', JSON.stringify(user));
              
              // Use window.location to force a full page load
              window.location.href = '/';
            }}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
          >
            Go to Home Page (Direct)
          </button>
        )}
        
        <button
          onClick={() => {
            // Remove any saved user data
            localStorage.removeItem('demo_mode');
            localStorage.removeItem('demo_user');
            setUser(null);
            queryClient.setQueryData(['/api/user'], null);
            toast({
              title: 'Data Cleared',
              description: 'Local storage has been cleared.'
            });
          }}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
        >
          Clear Saved Data
        </button>
        
        <a 
          href="/auth" 
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Go back to login page
        </a>
      </div>
    </div>
  );
}