
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>RecipeSafe Demo</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #ff6b35;
    }
    .login-form {
      margin-bottom: 20px;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    .recipe-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 20px;
    }
    .recipe-card {
      border: 1px solid #ddd;
      border-radius: 5px;
      overflow: hidden;
      transition: transform 0.2s;
    }
    .recipe-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    .recipe-image {
      width: 100%;
      height: 150px;
      object-fit: cover;
    }
    .recipe-content {
      padding: 15px;
    }
    .recipe-title {
      margin-top: 0;
      color: #333;
    }
    .recipe-description {
      color: #666;
      font-size: 14px;
    }
    button {
      background-color: #ff6b35;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background-color: #e85a2a;
    }
    input {
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      margin-right: 10px;
    }
  </style>
</head>
<body>
  <h1>RecipeSafe Demo</h1>

  <div class="login-form" id="login-form">
    <h2>Enter Password</h2>
    <p>Use the password: "happy days" to access the recipes</p>
    <input type="password" id="password" placeholder="Enter password">
    <button onclick="verifyPassword()">Login</button>
    <p id="login-error" style="color: red; display: none;">Incorrect password</p>
  </div>

  <div id="recipes-container" style="display: none;">
    <h2>Your Recipes</h2>
    <div class="recipe-grid" id="recipe-grid"></div>
  </div>

  <script>
    // Verify password
    async function verifyPassword() {
      const password = document.getElementById('password').value;

      try {
        const response = await fetch('/api/verify-password', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ password })
        });

        const data = await response.json();

        if (data.success) {
          document.getElementById('login-form').style.display = 'none';
          document.getElementById('recipes-container').style.display = 'block';
          loadRecipes();
        } else {
          document.getElementById('login-error').style.display = 'block';
        }
      } catch (error) {
        console.error('Error:', error);
      }
    }

    // Load recipes
    async function loadRecipes() {
      try {
        const response = await fetch('/api/recipes');
        const recipes = await response.json();

        const recipeGrid = document.getElementById('recipe-grid');
        recipeGrid.innerHTML = '';

        recipes.forEach(recipe => {
          const recipeCard = document.createElement('div');
          recipeCard.className = 'recipe-card';

          recipeCard.innerHTML = `
            <img src="${recipe.imageUrl}" alt="${recipe.title}" class="recipe-image">
            <div class="recipe-content">
              <h3 class="recipe-title">${recipe.title}</h3>
              <p class="recipe-description">${recipe.description}</p>
              <button onclick="viewRecipe(${recipe.id})">View Recipe</button>
            </div>
          `;

          recipeGrid.appendChild(recipeCard);
        });
      } catch (error) {
        console.error('Error:', error);
      }
    }

    // View recipe details
    async function viewRecipe(id) {
      try {
        const response = await fetch(`/api/recipes/${id}`);
        const recipeData = await response.json();

        const recipesContainer = document.getElementById('recipes-container');
        recipesContainer.innerHTML = `
          <button onclick="backToRecipes()">← Back to Recipes</button>
          <h2>${recipeData.recipe.title}</h2>
          <img src="${recipeData.recipe.imageUrl}" alt="${recipeData.recipe.title}" style="max-width: 100%; max-height: 300px; object-fit: cover;">
          <p>${recipeData.recipe.description}</p>

          <div style="display: flex; gap: 20px; margin: 20px 0;">
            <div>
              <h3>Prep Time</h3>
              <p>${recipeData.recipe.prepTime}</p>
            </div>
            <div>
              <h3>Cook Time</h3>
              <p>${recipeData.recipe.cookTime}</p>
            </div>
          </div>

          <h3>Ingredients</h3>
          <ul>
            ${recipeData.ingredients.map(ingredient => `
              <li>${ingredient.quantity} ${ingredient.unit} ${ingredient.name}</li>
            `).join('')}
          </ul>

          <h3>Instructions</h3>
          <ol>
            ${recipeData.steps.map(step => `
              <li>${step.instruction}</li>
            `).join('')}
          </ol>

          <h3>Notes</h3>
          <p>${recipeData.recipe.notes || 'No notes'}</p>

          <h3>Source</h3>
          <p><a href="${recipeData.recipe.sourceUrl}" target="_blank">${recipeData.recipe.sourceUrl}</a></p>
        `;
      } catch (error) {
        console.error('Error:', error);
      }
    }

    // Back to recipes list
    function backToRecipes() {
      document.getElementById('recipes-container').innerHTML = `
        <h2>Your Recipes</h2>
        <div class="recipe-grid" id="recipe-grid"></div>
      `;
      loadRecipes();
    }
  </script>
</body>
</html>
