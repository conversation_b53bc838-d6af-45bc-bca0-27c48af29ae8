import { InsertRecipe, InsertIngredient, InsertStep } from "@shared/schema";
import { JSDOM } from 'jsdom';

interface ParsedRecipe {
  recipe: Omit<InsertRecipe, "userId">;
  ingredients: Omit<InsertIngredient, "recipeId">[];
  steps: Omit<InsertStep, "recipeId">[];
  tags: string[];
}

export function enhancedParseHtml(html: string, sourceUrl: string): ParsedRecipe | null {
  try {
    console.log("Using enhanced HTML parser...");
    const dom = new JSDOM(html);
    const doc = dom.window.document;
    
    // ==================== Title Extraction ====================
    // Try multiple selectors for recipe title with priority order
    const titleSelectors = [
      'h1',
      'h1.recipe-title',
      '.recipe-title',
      '.recipe-header h1',
      '.recipe-container h1',
      '.post-title',
      '.entry-title',
      'article h1',
      'header h1',
      'title'
    ];
    
    let title = '';
    for (const selector of titleSelectors) {
      const element = doc.querySelector(selector);
      if (element && element.textContent) {
        title = element.textContent.trim();
        console.log(`Found title using selector: ${selector}`);
        break;
      }
    }
    
    // ==================== Description Extraction ====================
    // Try different selectors for description
    const descriptionSelectors = [
      'meta[name="description"]',
      '.recipe-description',
      '.recipe-summary',
      '.intro-text',
      '.post-content p:first-of-type',
      '.entry-content p:first-of-type',
      '.recipe-excerpt',
      'article .summary'
    ];
    
    let description = '';
    for (const selector of descriptionSelectors) {
      const element = doc.querySelector(selector);
      if (element) {
        if (selector.startsWith('meta')) {
          description = element.getAttribute('content') || '';
        } else {
          description = element.textContent || '';
        }
        if (description) {
          console.log(`Found description using selector: ${selector}`);
          break;
        }
      }
    }
    
    // ==================== Image Extraction ====================
    // Try different selectors for recipe image
    const imageSelectors = [
      'meta[property="og:image"]',
      '.recipe-image img',
      '.hero-image img',
      '.post-image img',
      '.featured-image img',
      'img.recipe-image',
      'article img:first-of-type',
      '.entry-content img:first-of-type'
    ];
    
    let imageUrl = '';
    for (const selector of imageSelectors) {
      const element = doc.querySelector(selector);
      if (element) {
        if (selector.startsWith('meta')) {
          imageUrl = element.getAttribute('content') || '';
        } else {
          imageUrl = element.getAttribute('src') || '';
        }
        if (imageUrl) {
          console.log(`Found image using selector: ${selector}`);
          break;
        }
      }
    }
    
    // If image URL is relative, convert to absolute
    if (imageUrl && !imageUrl.startsWith('http')) {
      try {
        const base = new URL(sourceUrl);
        imageUrl = new URL(imageUrl, base).toString();
      } catch (error) {
        console.log("Error converting relative URL to absolute:", error);
      }
    }
    
    // ==================== Prep/Cook Time Extraction ====================
    const timeSelectors = {
      prepTime: [
        'meta[itemprop="prepTime"]',
        '.prep-time',
        '.recipe-prep-time',
        '[itemprop="prepTime"]',
        '.recipe-meta .prep-time',
        'time[itemprop="prepTime"]'
      ],
      cookTime: [
        'meta[itemprop="cookTime"]',
        '.cook-time',
        '.recipe-cook-time',
        '[itemprop="cookTime"]',
        '.recipe-meta .cook-time',
        'time[itemprop="cookTime"]'
      ]
    };
    
    let prepTime = '';
    let cookTime = '';
    
    for (const selector of timeSelectors.prepTime) {
      const element = doc.querySelector(selector);
      if (element) {
        if (selector.startsWith('meta')) {
          prepTime = element.getAttribute('content') || '';
        } else {
          prepTime = element.textContent || '';
        }
        if (prepTime) {
          console.log(`Found prep time using selector: ${selector}`);
          break;
        }
      }
    }
    
    for (const selector of timeSelectors.cookTime) {
      const element = doc.querySelector(selector);
      if (element) {
        if (selector.startsWith('meta')) {
          cookTime = element.getAttribute('content') || '';
        } else {
          cookTime = element.textContent || '';
        }
        if (cookTime) {
          console.log(`Found cook time using selector: ${selector}`);
          break;
        }
      }
    }
    
    // ==================== Ingredients Extraction ====================
    // Try multiple selectors for ingredient lists with priority order
    const ingredientListSelectors = [
      '.ingredients',
      '.recipe-ingredients',
      '[itemprop="recipeIngredient"]',
      '.ingredient-list',
      'ul.ingredients',
      '.ingredients-section',
      '.wprm-recipe-ingredient-group',
      '.tasty-recipes-ingredients',
      '.recipe-ingredients-container'
    ];
    
    let ingredientElements: Element[] = [];
    
    // First try to find container elements that might have ingredients as direct children
    for (const selector of ingredientListSelectors) {
      const container = doc.querySelector(selector);
      if (container) {
        // Check for list items inside the container
        const items = container.querySelectorAll('li');
        if (items.length > 0) {
          ingredientElements = Array.from(items);
          console.log(`Found ${ingredientElements.length} ingredients using selector: ${selector} > li`);
          break;
        }
        
        // Check for specific ingredient items
        const ingredients = container.querySelectorAll('.ingredient, [itemprop="recipeIngredient"]');
        if (ingredients.length > 0) {
          ingredientElements = Array.from(ingredients);
          console.log(`Found ${ingredientElements.length} ingredients using selector: ${selector} > .ingredient`);
          break;
        }
        
        // Check for p tags which might contain ingredients
        const paragraphs = container.querySelectorAll('p');
        if (paragraphs.length > 0) {
          ingredientElements = Array.from(paragraphs);
          console.log(`Found ${ingredientElements.length} ingredients using selector: ${selector} > p`);
          break;
        }
        
        // As a last resort, use children of the container
        if (container.children.length > 0) {
          ingredientElements = Array.from(container.children);
          console.log(`Found ${ingredientElements.length} potential ingredients using children of: ${selector}`);
          break;
        }
      }
    }
    
    // If the above selectors didn't work, try some common ingredient patterns
    if (ingredientElements.length === 0) {
      // Try to find all li elements that might be ingredients
      const allLiElements = doc.querySelectorAll('li');
      const possibleIngredientLists = Array.from(allLiElements).filter(li => {
        const text = li.textContent || '';
        // Check if the text has patterns common in ingredients
        return (
          /[0-9]/.test(text) && // Has numbers (quantities)
          /(cup|tbsp|tsp|oz|pound|g|ml|teaspoon|tablespoon)/i.test(text) && // Has units
          text.length < 200 // Not too long (paragraphs)
        );
      });
      
      if (possibleIngredientLists.length > 0) {
        ingredientElements = possibleIngredientLists;
        console.log(`Found ${ingredientElements.length} potential ingredients by pattern matching`);
      }
    }
    
    // Process the ingredient elements to extract data
    const ingredients = ingredientElements.map((el, index) => {
      const text = el.textContent || '';
      const quantity = extractQuantity(text);
      const unit = extractUnit(text);
      // If quantity is in the name, don't include it twice
      let name = parseIngredientText(text);
      // Remove quantity from the beginning of the name if it starts with the quantity
      if (quantity && name.startsWith(quantity)) {
        name = name.substring(quantity.length).trim();
      }
      // Remove unit from the beginning of the name if it starts with the unit
      if (unit && name.toLowerCase().startsWith(unit.toLowerCase())) {
        name = name.substring(unit.length).trim();
      }
      
      return {
        name: name,
        quantity: quantity,
        unit: unit
      };
    });
    
    // ==================== Instructions Extraction ====================
    // Try multiple selectors for instruction lists with priority order
    const instructionListSelectors = [
      '.instructions',
      '.recipe-instructions',
      '[itemprop="recipeInstructions"]',
      '.recipe-directions',
      '.preparation',
      '.method',
      '.recipe-method',
      '.wprm-recipe-instructions',
      '.tasty-recipes-instructions',
      '.directions-area'
    ];
    
    let instructionElements: Element[] = [];
    
    // Try to find container elements that might have instructions
    for (const selector of instructionListSelectors) {
      const container = doc.querySelector(selector);
      if (container) {
        // Check for ordered list items
        const liItems = container.querySelectorAll('li, ol > li');
        if (liItems.length > 0) {
          instructionElements = Array.from(liItems);
          console.log(`Found ${instructionElements.length} instructions using selector: ${selector} > li`);
          break;
        }
        
        // Check for div or p elements which might contain instructions
        const steps = container.querySelectorAll('.step, .instruction, p');
        if (steps.length > 0) {
          instructionElements = Array.from(steps);
          console.log(`Found ${instructionElements.length} instructions using selector: ${selector} > p or .step`);
          break;
        }
        
        // As a last resort, use children of the container
        if (container.children.length > 0) {
          instructionElements = Array.from(container.children);
          console.log(`Found ${instructionElements.length} potential instructions using children of: ${selector}`);
          break;
        }
      }
    }
    
    // If we still don't have instructions, try some basic patterns
    if (instructionElements.length === 0) {
      // Look for ordered lists (usually instructions)
      const olElements = doc.querySelectorAll('ol');
      for (const ol of Array.from(olElements)) {
        const liItems = ol.querySelectorAll('li');
        if (liItems.length > 0) {
          // Check if these look like instructions (longer text, not ingredient-like)
          const firstItem = liItems[0].textContent || '';
          if (firstItem.length > 50 && !/^\d+\s*(cup|tbsp|tsp|oz)/i.test(firstItem)) {
            instructionElements = Array.from(liItems);
            console.log(`Found ${instructionElements.length} potential instructions from ordered list`);
            break;
          }
        }
      }
    }
    
    // Process the instruction elements
    const steps = instructionElements.map((el, index) => ({
      stepNumber: index + 1,
      instruction: (el.textContent || '').trim()
    }));
    
    // ==================== Tags Extraction ====================
    // Try to extract recipe tags/categories
    const tagSelectors = [
      'meta[name="keywords"]',
      '.tags',
      '.recipe-tags',
      '.categories',
      '.recipe-categories',
      '[itemprop="keywords"]',
      '.post-categories',
      '.entry-categories'
    ];
    
    let tagsList: string[] = [];
    
    for (const selector of tagSelectors) {
      const element = doc.querySelector(selector);
      if (element) {
        if (selector.startsWith('meta')) {
          const keywords = element.getAttribute('content') || '';
          if (keywords) {
            tagsList = keywords.split(',').map(tag => tag.trim());
          }
        } else {
          const links = element.querySelectorAll('a');
          if (links.length > 0) {
            tagsList = Array.from(links).map(link => (link.textContent || '').trim());
          } else {
            const text = element.textContent || '';
            if (text) {
              tagsList = text.split(',').map(tag => tag.trim());
            }
          }
        }
        
        if (tagsList.length > 0) {
          console.log(`Found ${tagsList.length} tags using selector: ${selector}`);
          break;
        }
      }
    }
    
    // Remove any empty tags and limit to a reasonable number
    tagsList = tagsList.filter(tag => tag && tag.length > 0).slice(0, 10);
    
    return {
      recipe: {
        title: title.trim(),
        description: description.trim(),
        imageUrl,
        prepTime,
        cookTime,
        sourceUrl,
        notes: ''
      },
      ingredients,
      steps,
      tags: tagsList
    };
  } catch (error) {
    console.error("Error in enhanced HTML parsing:", error);
    return null;
  }
}

// Helper functions for ingredient parsing
function parseIngredientText(ingredient: string): string {
  return ingredient.trim();
}

function extractQuantity(ingredient: string): string {
  // Match common quantity patterns (e.g., 1, 1.5, 1/2, 1 1/2)
  const quantityMatch = ingredient.match(/^(\d+(\s+\d+\/\d+|\.\d+|\/\d+)?)/);
  return quantityMatch ? quantityMatch[0].trim() : '';
}

function extractUnit(ingredient: string): string {
  const commonUnits = [
    'cup', 'cups', 
    'tbsp', 'tablespoon', 'tablespoons', 
    'tsp', 'teaspoon', 'teaspoons', 
    'oz', 'ounce', 'ounces', 
    'lb', 'pound', 'pounds', 
    'g', 'gram', 'grams', 
    'kg', 'kilogram', 'kilograms', 
    'ml', 'milliliter', 'milliliters', 
    'l', 'liter', 'liters',
    'pinch', 'dash',
    'clove', 'cloves',
    'can', 'cans',
    'slice', 'slices',
    'bunch', 'bunches'
  ];
  
  // Remove any quantity and spacing at the beginning
  const ingredientWithoutQuantity = ingredient.replace(/^(\d+(\s+\d+\/\d+|\.\d+|\/\d+)?)\s*/, '');
  
  for (const unit of commonUnits) {
    // Check for the unit at the beginning of the ingredient (after quantity)
    const unitRegex = new RegExp(`^(${unit}s?\\b|${unit}\\.)`, 'i');
    if (unitRegex.test(ingredientWithoutQuantity)) {
      return unit;
    }
    
    // Also check for units with spaces
    const spaceUnitRegex = new RegExp(`\\s+(${unit}s?\\b|${unit}\\.)`, 'i');
    if (spaceUnitRegex.test(ingredient)) {
      return unit;
    }
  }
  
  return '';
}