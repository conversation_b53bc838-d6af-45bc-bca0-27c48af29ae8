import { GoogleGenerativeAI } from "@google/generative-ai";
import { InsertRecipe, InsertIngredient, InsertStep } from "@shared/schema";
import fetch from 'node-fetch';

interface ParsedRecipe {
  recipe: Omit<InsertRecipe, "userId">;
  ingredients: Omit<InsertIngredient, "recipeId">[];
  steps: Omit<InsertStep, "recipeId">[];
  tags: string[];
}

export async function parseRecipeWithGemini(url: string, htmlContent: string): Promise<ParsedRecipe | null> {
  try {
    if (!process.env.GEMINI_API_KEY) {
      console.error("GEMINI_API_KEY is not set in environment variables");
      return null;
    }

    // Initialize Gemini
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    const model = genAI.getGenerativeModel({ model: "gemini-pro" });

    console.log("Analyzing recipe with Gemini AI...");

    // Prepare prompt for Gemini
    const prompt = `
      You are a recipe parsing expert. Extract structured information from the following recipe HTML.
      Return your analysis as a valid JSON object with the following structure:
      {
        "title": "Recipe Title",
        "description": "Recipe description or summary",
        "prepTime": "Preparation time (e.g., '15 minutes')",
        "cookTime": "Cooking time (e.g., '30 minutes')",
        "totalTime": "Total time (e.g., '45 minutes')",
        "servings": "Number of servings",
        "ingredients": [
          { "name": "Ingredient name", "quantity": "Amount", "unit": "Measurement unit" }
        ],
        "instructions": [
          { "stepNumber": 1, "instruction": "Step instruction text" }
        ],
        "tags": ["tag1", "tag2"],
        "notes": "Additional notes or tips",
        "imageUrl": "URL to recipe image if found"
      }

      Include as much information as possible from the HTML. Leave fields empty if not found.
      For ingredients, try to separate quantity, unit, and name properly.
      For instructions, maintain the original order.
      Recipe HTML from URL (${url}):
      ${htmlContent.substring(0, 30000)} // Limit size to avoid token overflow
    `;

    // Generate content
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const responseText = response.text();

    // Extract JSON from response
    const jsonMatch = responseText.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      console.error("No JSON found in Gemini response");
      return null;
    }

    const parsedData = JSON.parse(jsonMatch[0]);
    console.log("Gemini successfully analyzed the recipe!");

    // Transform Gemini response to our ParsedRecipe format
    return {
      recipe: {
        title: parsedData.title || "",
        description: parsedData.description || "",
        prepTime: parsedData.prepTime || "",
        cookTime: parsedData.cookTime || "",
        imageUrl: parsedData.imageUrl || "",
        sourceUrl: url,
        notes: parsedData.notes || ""
      },
      ingredients: parsedData.ingredients?.map((ing: any, index: number) => ({
        name: ing.name || "",
        quantity: ing.quantity || "",
        unit: ing.unit || ""
      })) || [],
      steps: parsedData.instructions?.map((step: any) => ({
        stepNumber: step.stepNumber || 0,
        instruction: step.instruction || ""
      })) || [],
      tags: parsedData.tags || []
    };

  } catch (error) {
    console.error("Error parsing recipe with Gemini:", error);
    return null;
  }
}