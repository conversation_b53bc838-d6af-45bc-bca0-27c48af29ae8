import { InsertRecipe, InsertIngredient, InsertStep } from "@shared/schema";
// @ts-ignore - Ignore missing type definitions for jsdom
import { JSDOM } from 'jsdom';
// @ts-ignore - Ignore missing type definitions for node-fetch
import fetch from 'node-fetch';
import { enhancedParseHtml } from './enhancedRecipeParser';
import { parseRecipeWithGemini } from './geminiRecipeParser';

interface ParsedRecipe {
  recipe: Omit<InsertRecipe, "userId">;
  ingredients: Omit<InsertIngredient, "recipeId">[];
  steps: Omit<InsertStep, "recipeId">[];
  tags: string[];
}

export async function parseRecipeFromUrl(url: string): Promise<ParsedRecipe | null> {
  try {
    // Validate the URL
    new URL(url);
    
    console.log(`Fetching recipe from URL: ${url}`);
    
    // Fetch the HTML content
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch URL: ${response.statusText}`);
    }
    
    const html = await response.text();
    console.log(`Fetched HTML content from ${url} (${html.length} bytes)`);
    
    // Store all parsing results to merge for best data
    let parsedResults: (ParsedRecipe | null)[] = [];
    
    // First attempt: Check for structured data (JSON-LD)
    console.log("Attempting to extract structured data (JSON-LD)...");
    const structuredData = extractStructuredData(html);
    if (structuredData) {
      console.log("Successfully extracted structured data");
      parsedResults.push(structuredData);
      
      // If we got lots of data from structured data, return right away
      if (structuredData.ingredients.length > 3 && structuredData.steps.length > 2) {
        return structuredData;
      }
    }
    
    // Second attempt: Use enhanced HTML parser
    console.log("Using enhanced HTML parser for recipe extraction...");
    const enhancedResult = enhancedParseHtml(html, url);
    if (enhancedResult) {
      console.log("Successfully parsed recipe with enhanced parser");
      parsedResults.push(enhancedResult);
      
      // If we have good results from enhanced parser, don't continue to basic parser
      if (enhancedResult.ingredients.length > 3 && enhancedResult.steps.length > 2) {
        // If we already have structured data, merge the results
        if (structuredData) {
          return mergeRecipeResults([structuredData, enhancedResult]);
        }
        return enhancedResult;
      }
    }
    
    // Third attempt: Basic HTML parsing
    console.log("Using basic HTML parsing...");
    const basicResult = parseHtml(html, url);
    if (basicResult) {
      parsedResults.push(basicResult);
    }
    
    // Final attempt: If we have a Gemini API key, try AI-based parsing
    if (process.env.GEMINI_API_KEY) {
      console.log("Using Gemini AI for recipe extraction...");
      const geminiResult = await parseRecipeWithGemini(url, html);
      if (geminiResult) {
        parsedResults.push(geminiResult);
      }
    }
    
    // If we have multiple parsing results, merge them to get the most complete data
    if (parsedResults.length > 0) {
      return mergeRecipeResults(parsedResults.filter(result => result !== null) as ParsedRecipe[]);
    }
    
    // If all parsing methods failed, return null
    console.log("All parsing methods failed. No recipe data extracted.");
    return null;
    
  } catch (error) {
    console.error("Recipe parsing error:", error);
    return null;
  }
}

// Function to merge results from different parsers to get the most complete data
function mergeRecipeResults(results: ParsedRecipe[]): ParsedRecipe {
  if (results.length === 0) {
    throw new Error("Cannot merge empty results array");
  }
  if (results.length === 1) {
    return results[0];
  }
  
  // Start with the first result as base
  const merged: ParsedRecipe = structuredClone(results[0]);
  
  // Go through all other results and fill in missing data
  for (let i = 1; i < results.length; i++) {
    const result = results[i];
    
    // Merge recipe basic data
    const recipeData = merged.recipe;
    if (!recipeData.title && result.recipe.title) recipeData.title = result.recipe.title;
    if (!recipeData.description && result.recipe.description) recipeData.description = result.recipe.description;
    if (!recipeData.imageUrl && result.recipe.imageUrl) recipeData.imageUrl = result.recipe.imageUrl;
    if (!recipeData.prepTime && result.recipe.prepTime) recipeData.prepTime = result.recipe.prepTime;
    if (!recipeData.cookTime && result.recipe.cookTime) recipeData.cookTime = result.recipe.cookTime;
    if (!recipeData.notes && result.recipe.notes) recipeData.notes = result.recipe.notes;
    
    // Use the longest list of ingredients
    if (merged.ingredients.length < result.ingredients.length) {
      merged.ingredients = result.ingredients;
    }
    
    // Use the longest list of steps
    if (merged.steps.length < result.steps.length) {
      merged.steps = result.steps;
    }
    
    // Merge tags
    const uniqueTags = new Set([...merged.tags, ...result.tags]);
    merged.tags = Array.from(uniqueTags);
  }
  
  console.log(`Merged ${results.length} parsing results for best data`);
  return merged;
}

function extractStructuredData(html: string): ParsedRecipe | null {
  try {
    // Look for JSON-LD
    const jsonLdMatch = html.match(/<script type="application\/ld\+json">([\s\S]*?)<\/script>/);
    if (jsonLdMatch && jsonLdMatch[1]) {
      const jsonData = JSON.parse(jsonLdMatch[1]);
      
      // Check if it's a Recipe schema
      if (jsonData["@type"] === "Recipe" || 
          (Array.isArray(jsonData["@graph"]) && 
           jsonData["@graph"].some((item: any) => item["@type"] === "Recipe"))) {
        
        // Extract recipe data from JSON-LD
        const recipeData = jsonData["@type"] === "Recipe" ? 
                          jsonData : 
                          jsonData["@graph"].find((item: any) => item["@type"] === "Recipe");
        
        if (!recipeData) return null;
        
        // Parse ingredients
        const ingredients = Array.isArray(recipeData.recipeIngredient) 
          ? recipeData.recipeIngredient.map((ing: string, index: number) => ({
              name: parseIngredientText(ing),
              quantity: extractQuantity(ing),
              unit: extractUnit(ing)
            }))
          : [];
        
        // Parse instructions
        const instructions = Array.isArray(recipeData.recipeInstructions)
          ? recipeData.recipeInstructions.map((inst: any, index: number) => ({
              stepNumber: index + 1,
              instruction: typeof inst === 'string' ? inst : inst.text || ''
            }))
          : [];
        
        // Extract tags/keywords
        let tags: string[] = [];
        if (recipeData.keywords) {
          tags = typeof recipeData.keywords === 'string' 
            ? recipeData.keywords.split(',').map((t: string) => t.trim()) 
            : Array.isArray(recipeData.keywords) ? recipeData.keywords : [];
        }
        
        return {
          recipe: {
            title: recipeData.name || '',
            description: recipeData.description || '',
            imageUrl: recipeData.image || '',
            prepTime: recipeData.prepTime || '',
            cookTime: recipeData.cookTime || '',
            sourceUrl: recipeData.url || '',
            notes: ''
          },
          ingredients,
          steps: instructions,
          tags
        };
      }
    }
    
    return null;
  } catch (error) {
    console.error("Error extracting structured data:", error);
    return null;
  }
}

function parseHtml(html: string, sourceUrl: string): ParsedRecipe | null {
  try {
    // Create a DOM with JSDOM
    const dom = new JSDOM(html);
    const doc = dom.window.document;
    
    // Try to extract title
    const title = doc.querySelector('h1')?.textContent || 
                 doc.querySelector('.recipe-title')?.textContent || 
                 doc.querySelector('title')?.textContent || '';
    
    // Try to extract description
    const description = doc.querySelector('meta[name="description"]')?.getAttribute('content') || '';
    
    // Try to extract image
    const imageUrl = doc.querySelector('meta[property="og:image"]')?.getAttribute('content') || '';
    
    // Best effort to extract ingredients (common patterns in recipe sites)
    const ingredientsList = Array.from(doc.querySelectorAll('.ingredients li, .ingredient-list li, [itemprop="recipeIngredient"]'));
    const ingredients = ingredientsList.map((el: Element, index) => ({
      name: parseIngredientText(el.textContent || ''),
      quantity: extractQuantity(el.textContent || ''),
      unit: extractUnit(el.textContent || '')
    }));
    
    // Best effort to extract instructions
    const instructionsList = Array.from(doc.querySelectorAll('.instructions li, .steps li, [itemprop="recipeInstructions"]'));
    const steps = instructionsList.map((el: Element, index) => ({
      stepNumber: index + 1,
      instruction: el.textContent || ''
    }));
    
    return {
      recipe: {
        title: title.trim(),
        description: description.trim(),
        imageUrl,
        prepTime: '',
        cookTime: '',
        sourceUrl,
        notes: ''
      },
      ingredients,
      steps,
      tags: []
    };
  } catch (error) {
    console.error("Error parsing HTML:", error);
    return null;
  }
}

// Helper functions for ingredient parsing
function parseIngredientText(ingredient: string): string {
  // Remove the quantity and unit from the beginning
  const withoutQuantity = ingredient.replace(/^[\d\s\/\.\,]+/, '').trim();
  
  // Remove common units from the beginning
  const unitRegex = /^(cup|cups|tbsp|tablespoon|tablespoons|tsp|teaspoon|teaspoons|oz|ounce|ounces|lb|pound|pounds|g|gram|grams|kg|kilogram|kilograms|ml|milliliter|milliliters|l|liter|liters|pinch|dash|clove|cloves|can|cans|slice|slices|bunch|bunches)s?\s+/i;
  const withoutUnit = withoutQuantity.replace(unitRegex, '');
  
  // Clean up extra spaces and punctuation
  return withoutUnit.replace(/^\s*of\s+/i, '').trim();
}

function extractQuantity(ingredient: string): string {
  // Handle common fraction characters
  const fractionMap: {[key: string]: string} = {
    '½': '1/2', '⅓': '1/3', '⅔': '2/3', '¼': '1/4', '¾': '3/4',
    '⅕': '1/5', '⅖': '2/5', '⅗': '3/5', '⅘': '4/5', '⅙': '1/6',
    '⅚': '5/6', '⅛': '1/8', '⅜': '3/8', '⅝': '5/8', '⅞': '7/8'
  };
  
  let updatedIngredient = ingredient;
  for (const [fraction, replacement] of Object.entries(fractionMap)) {
    updatedIngredient = updatedIngredient.replace(fraction, replacement);
  }
  
  // Complex regex to match various quantity formats:
  // - Simple numbers: 1, 2, 3
  // - Decimals: 1.5, 2.25
  // - Fractions: 1/2, 3/4
  // - Mixed numbers: 1 1/2, 2 1/4
  // - Ranges: 1-2, 1 to 2
  const quantityRegex = /^(\d+(\s+\d+\/\d+|\.\d+|\/\d+)?\s*(to|-)\s*\d+(\s+\d+\/\d+|\.\d+|\/\d+)?|\d+(\s+\d+\/\d+|\.\d+|\/\d+)?)/;
  
  const match = updatedIngredient.match(quantityRegex);
  return match ? match[0].trim() : '';
}

function extractUnit(ingredient: string): string {
  const commonUnits = [
    // Volume measurements
    'cup', 'cups', 'tbsp', 'tablespoon', 'tablespoons', 
    'tsp', 'teaspoon', 'teaspoons', 'fluid ounce', 'fl oz',
    'pint', 'pints', 'quart', 'quarts', 'gallon', 'gallons',
    'ml', 'milliliter', 'milliliters', 'l', 'liter', 'liters',
    
    // Weight measurements
    'oz', 'ounce', 'ounces', 'lb', 'pound', 'pounds', 
    'g', 'gram', 'grams', 'kg', 'kilogram', 'kilograms',
    
    // Small amounts
    'pinch', 'pinches', 'dash', 'dashes', 'sprinkle', 'sprinkles',
    'drop', 'drops',
    
    // Other common units
    'clove', 'cloves', 'bunch', 'bunches', 'sprig', 'sprigs',
    'can', 'cans', 'jar', 'jars', 'package', 'packages',
    'slice', 'slices', 'piece', 'pieces', 'stalk', 'stalks',
    'head', 'heads', 'bulb', 'bulbs', 'leaf', 'leaves',
    'stick', 'sticks', 'strip', 'strips'
  ];
  
  // First, remove any quantity from the string
  const withoutQuantity = ingredient.replace(/^[\d\s\/\.\,]+/, '').trim();
  
  // Try to match a unit at the beginning of the ingredient (after quantity)
  for (const unit of commonUnits) {
    // Check if the ingredient starts with this unit word
    const unitRegex = new RegExp(`^(${unit}s?\\b|${unit}\\.)`, 'i');
    if (unitRegex.test(withoutQuantity)) {
      return unit;
    }
  }
  
  // If not found at beginning, check within the first few words
  const ingredientLower = ingredient.toLowerCase();
  const firstFewWords = ingredientLower.split(' ').slice(0, 3).join(' ');
  
  for (const unit of commonUnits) {
    if (firstFewWords.includes(` ${unit} `) || 
        firstFewWords.includes(` ${unit}s `) || 
        firstFewWords.includes(`${unit} `)) {
      return unit;
    }
  }
  
  return '';
}
