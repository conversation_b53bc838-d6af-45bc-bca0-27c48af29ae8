import type { Express } from "express";
import { createServer } from "http";
import type { Server } from "http";
import { storage } from "./storage";
import { parseRecipeFromUrl } from "./recipeParser";

// Static user ID for the Family user account
const FAMILY_USER_ID = 1;

// Simplified authentication middleware - always use the Family user
function useSimpleAuth(req: any, res: any, next: any) {
  // Set the user ID to Family user
  req.user = { id: FAMILY_USER_ID };
  return next();
}

// Password verification endpoint
function setupSimpleAuth(app: Express) {
  // Simple password verification endpoint
  app.post("/api/verify-password", (req, res) => {
    const { password } = req.body;
    
    console.log("Verifying password");
    
    if (password === "happy days") {
      console.log("Password verified successfully");
      res.json({ success: true });
    } else {
      console.log("Password verification failed");
      res.status(401).json({ 
        success: false, 
        message: "Incorrect password" 
      });
    }
  });
}

export async function registerSimpleRoutes(app: Express): Promise<Server> {
  // Initialize the HTTP server
  const server = createServer(app);
  
  // Setup the simple password verification endpoint
  setupSimpleAuth(app);
  
  // Create the Family user if it doesn't exist
  try {
    const existingUser = await storage.getUserByUsername("Family");
    if (!existingUser) {
      console.log("Creating Family user");
      await storage.createUser({
        id: FAMILY_USER_ID,
        username: "Family",
        email: "<EMAIL>",
        password: "happy days", // This won't be used for direct login anymore
      });
      console.log("Created Family user with ID:", FAMILY_USER_ID);
    } else {
      console.log("Family user already exists with ID:", existingUser.id);
    }
  } catch (error) {
    console.error("Error setting up Family user:", error);
  }
  
  // ===== Recipe routes =====
  app.get("/api/recipes", useSimpleAuth, async (req, res) => {
    try {
      const userId = req.user.id;
      const recipes = await storage.getRecipesByUserId(userId);
      res.json(recipes);
    } catch (error) {
      console.error("Error fetching recipes:", error);
      res.status(500).json({ message: "Failed to fetch recipes" });
    }
  });
  
  app.get("/api/recipes/:id", useSimpleAuth, async (req, res) => {
    try {
      console.log(`Fetching recipe details for ID: ${req.params.id}`);
      const userId = req.user.id;
      const recipeId = parseInt(req.params.id);
      
      if (isNaN(recipeId)) {
        console.log(`Invalid recipe ID: ${req.params.id}`);
        return res.status(400).json({ message: "Invalid recipe ID" });
      }
      
      console.log(`Looking up recipe with ID: ${recipeId}`);
      const recipe = await storage.getRecipeById(recipeId);
      console.log(`Recipe lookup result:`, recipe ? "Found" : "Not found");
      
      if (!recipe) {
        return res.status(404).json({ message: "Recipe not found" });
      }
      
      // Get recipe details
      console.log(`Getting details for recipe ${recipeId}`);
      const ingredients = await storage.getIngredientsByRecipeId(recipeId);
      const steps = await storage.getStepsByRecipeId(recipeId);
      const tags = await storage.getTagsByRecipeId(recipeId);
      const isFavorite = await storage.isRecipeFavorite(userId, recipeId);
      
      console.log(`Recipe details: ${ingredients.length} ingredients, ${steps.length} steps, ${tags.length} tags`);
      
      const response = {
        recipe,
        ingredients,
        steps,
        tags,
        isFavorite
      };
      
      console.log(`Sending response for recipe ${recipeId}`);
      res.json(response);
    } catch (error) {
      console.error(`Error fetching recipe details: ${error}`);
      res.status(500).json({ message: "Failed to fetch recipe details" });
    }
  });
  
  app.post("/api/recipes", useSimpleAuth, async (req, res) => {
    try {
      const userId = req.user.id;
      
      // Validate recipe data
      const recipeData = {
        ...req.body.recipe,
        userId
      };
      
      console.log("Creating recipe:", recipeData.title);
      const recipe = await storage.createRecipe(recipeData);
      
      // Process ingredients
      if (Array.isArray(req.body.ingredients)) {
        for (const ingredient of req.body.ingredients) {
          await storage.createIngredient({
            ...ingredient,
            recipeId: recipe.id
          });
        }
      }
      
      // Process steps
      if (Array.isArray(req.body.steps)) {
        for (const step of req.body.steps) {
          await storage.createStep({
            ...step,
            recipeId: recipe.id
          });
        }
      }
      
      // Process tags
      if (Array.isArray(req.body.tags)) {
        for (const tagName of req.body.tags) {
          let tag = await storage.getTagByName(tagName);
          
          if (!tag) {
            tag = await storage.createTag({ name: tagName });
          }
          
          await storage.createRecipeTag({
            recipeId: recipe.id,
            tagId: tag.id
          });
        }
      }
      
      res.status(201).json({ recipeId: recipe.id });
    } catch (error) {
      console.error("Error creating recipe:", error);
      res.status(500).json({ message: "Failed to create recipe" });
    }
  });
  
  app.put("/api/recipes/:id", useSimpleAuth, async (req, res) => {
    try {
      const userId = req.user.id;
      const recipeId = parseInt(req.params.id);
      
      if (isNaN(recipeId)) {
        return res.status(400).json({ message: "Invalid recipe ID" });
      }
      
      const existingRecipe = await storage.getRecipeById(recipeId);
      
      if (!existingRecipe) {
        return res.status(404).json({ message: "Recipe not found" });
      }
      
      // Update recipe
      await storage.updateRecipe(recipeId, req.body.recipe);
      
      // Delete existing ingredients and steps
      await storage.deleteIngredientsByRecipeId(recipeId);
      await storage.deleteStepsByRecipeId(recipeId);
      
      // Add new ingredients
      if (Array.isArray(req.body.ingredients)) {
        for (const ingredient of req.body.ingredients) {
          await storage.createIngredient({
            ...ingredient,
            recipeId
          });
        }
      }
      
      // Add new steps
      if (Array.isArray(req.body.steps)) {
        for (const step of req.body.steps) {
          await storage.createStep({
            ...step,
            recipeId
          });
        }
      }
      
      // Update tags
      if (Array.isArray(req.body.tags)) {
        await storage.deleteRecipeTagsByRecipeId(recipeId);
        
        for (const tagName of req.body.tags) {
          let tag = await storage.getTagByName(tagName);
          
          if (!tag) {
            tag = await storage.createTag({ name: tagName });
          }
          
          await storage.createRecipeTag({
            recipeId,
            tagId: tag.id
          });
        }
      }
      
      res.json({ message: "Recipe updated successfully" });
    } catch (error) {
      res.status(500).json({ message: "Failed to update recipe" });
    }
  });
  
  app.delete("/api/recipes/:id", useSimpleAuth, async (req, res) => {
    try {
      const recipeId = parseInt(req.params.id);
      
      if (isNaN(recipeId)) {
        return res.status(400).json({ message: "Invalid recipe ID" });
      }
      
      const recipe = await storage.getRecipeById(recipeId);
      
      if (!recipe) {
        return res.status(404).json({ message: "Recipe not found" });
      }
      
      // Delete the recipe and all related data
      await storage.deleteRecipe(recipeId);
      
      res.json({ message: "Recipe deleted successfully" });
    } catch (error) {
      res.status(500).json({ message: "Failed to delete recipe" });
    }
  });
  
  // Recipe Import
  app.post("/api/recipes/import", useSimpleAuth, async (req, res) => {
    try {
      const userId = req.user.id;
      const { url } = req.body;
      
      if (!url) {
        return res.status(400).json({ message: "URL is required" });
      }
      
      console.log("Importing recipe from URL:", url);
      
      // First validate the URL format to give an early error if malformed
      try {
        new URL(url);
      } catch (e) {
        return res.status(400).json({ message: "Invalid URL format" });
      }
      
      // Show a more informative message while waiting for import
      res.setTimeout(60000); // Extend timeout to 60 seconds for AI parsing
      
      // Parse the recipe with our enhanced algorithm
      const parsedRecipe = await parseRecipeFromUrl(url);
      
      if (!parsedRecipe) {
        console.error("Failed to parse recipe from URL:", url);
        return res.status(400).json({ 
          message: "We couldn't import this recipe. This might be because the website doesn't have structured recipe data, or it's protected from being accessed programmatically. Try copying and pasting the recipe manually."
        });
      }
      
      // Check if we have enough data to consider this a successful import
      const hasMinimalData = parsedRecipe.recipe.title && 
                           (parsedRecipe.ingredients.length > 0 || 
                            parsedRecipe.steps.length > 0);
      
      if (!hasMinimalData) {
        console.log("Recipe parsed, but insufficient data was extracted");
        return res.status(400).json({ 
          message: "We could only extract minimal data from this recipe. Consider adding the details manually.",
          partialData: parsedRecipe
        });
      }
      
      console.log("Successfully parsed recipe:", parsedRecipe.recipe.title);
      console.log(`Extracted ${parsedRecipe.ingredients.length} ingredients and ${parsedRecipe.steps.length} steps`);
      
      res.json(parsedRecipe);
    } catch (error) {
      console.error("Recipe import error:", error);
      res.status(500).json({ 
        message: "Failed to import recipe. Our system encountered a problem processing this request. Please try again later."
      });
    }
  });
  
  // ===== Folder routes =====
  app.get("/api/folders", useSimpleAuth, async (req, res) => {
    try {
      const userId = req.user.id;
      
      const folders = await storage.getFoldersByUserId(userId);
      
      // Get recipe counts for each folder
      const foldersWithCounts = await Promise.all(
        folders.map(async (folder) => {
          const count = await storage.getRecipeCountByFolderId(folder.id);
          return {
            ...folder,
            recipeCount: count
          };
        })
      );
      
      res.json(foldersWithCounts);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch folders" });
    }
  });
  
  app.post("/api/folders", useSimpleAuth, async (req, res) => {
    try {
      const userId = req.user.id;
      const folderData = {
        ...req.body,
        userId
      };
      
      const folder = await storage.createFolder(folderData);
      res.status(201).json(folder);
    } catch (error) {
      res.status(500).json({ message: "Failed to create folder" });
    }
  });
  
  app.put("/api/folders/:id", useSimpleAuth, async (req, res) => {
    try {
      const folderId = parseInt(req.params.id);
      
      if (isNaN(folderId)) {
        return res.status(400).json({ message: "Invalid folder ID" });
      }
      
      const folder = await storage.getFolderById(folderId);
      
      if (!folder) {
        return res.status(404).json({ message: "Folder not found" });
      }
      
      await storage.updateFolder(folderId, req.body);
      res.json({ message: "Folder updated successfully" });
    } catch (error) {
      res.status(500).json({ message: "Failed to update folder" });
    }
  });
  
  app.delete("/api/folders/:id", useSimpleAuth, async (req, res) => {
    try {
      const folderId = parseInt(req.params.id);
      
      if (isNaN(folderId)) {
        return res.status(400).json({ message: "Invalid folder ID" });
      }
      
      const folder = await storage.getFolderById(folderId);
      
      if (!folder) {
        return res.status(404).json({ message: "Folder not found" });
      }
      
      await storage.deleteFolder(folderId);
      res.json({ message: "Folder deleted successfully" });
    } catch (error) {
      res.status(500).json({ message: "Failed to delete folder" });
    }
  });
  
  // ===== Folder-Recipe associations =====
  app.post("/api/folders/:folderId/recipes/:recipeId", useSimpleAuth, async (req, res) => {
    try {
      const folderId = parseInt(req.params.folderId);
      const recipeId = parseInt(req.params.recipeId);
      
      if (isNaN(folderId) || isNaN(recipeId)) {
        return res.status(400).json({ message: "Invalid folder or recipe ID" });
      }
      
      const folder = await storage.getFolderById(folderId);
      const recipe = await storage.getRecipeById(recipeId);
      
      if (!folder || !recipe) {
        return res.status(404).json({ message: "Folder or recipe not found" });
      }
      
      await storage.addRecipeToFolder({ folderId, recipeId });
      res.json({ message: "Recipe added to folder successfully" });
    } catch (error) {
      res.status(500).json({ message: "Failed to add recipe to folder" });
    }
  });
  
  app.delete("/api/folders/:folderId/recipes/:recipeId", useSimpleAuth, async (req, res) => {
    try {
      const folderId = parseInt(req.params.folderId);
      const recipeId = parseInt(req.params.recipeId);
      
      if (isNaN(folderId) || isNaN(recipeId)) {
        return res.status(400).json({ message: "Invalid folder or recipe ID" });
      }
      
      const folder = await storage.getFolderById(folderId);
      const recipe = await storage.getRecipeById(recipeId);
      
      if (!folder || !recipe) {
        return res.status(404).json({ message: "Folder or recipe not found" });
      }
      
      await storage.removeRecipeFromFolder(folderId, recipeId);
      res.json({ message: "Recipe removed from folder successfully" });
    } catch (error) {
      res.status(500).json({ message: "Failed to remove recipe from folder" });
    }
  });
  
  // ===== Favorites =====
  app.get("/api/favorites", useSimpleAuth, async (req, res) => {
    try {
      const userId = req.user.id;
      const favorites = await storage.getFavoritesByUserId(userId);
      res.json(favorites);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch favorites" });
    }
  });
  
  app.post("/api/favorites/:recipeId", useSimpleAuth, async (req, res) => {
    try {
      const userId = req.user.id;
      const recipeId = parseInt(req.params.recipeId);
      
      if (isNaN(recipeId)) {
        return res.status(400).json({ message: "Invalid recipe ID" });
      }
      
      const recipe = await storage.getRecipeById(recipeId);
      
      if (!recipe) {
        return res.status(404).json({ message: "Recipe not found" });
      }
      
      await storage.addFavorite({ userId, recipeId });
      res.json({ message: "Added to favorites" });
    } catch (error) {
      res.status(500).json({ message: "Failed to add to favorites" });
    }
  });
  
  app.delete("/api/favorites/:recipeId", useSimpleAuth, async (req, res) => {
    try {
      const userId = req.user.id;
      const recipeId = parseInt(req.params.recipeId);
      
      if (isNaN(recipeId)) {
        return res.status(400).json({ message: "Invalid recipe ID" });
      }
      
      await storage.removeFavorite(userId, recipeId);
      res.json({ message: "Removed from favorites" });
    } catch (error) {
      res.status(500).json({ message: "Failed to remove from favorites" });
    }
  });
  
  // ===== Search =====
  app.get("/api/search", useSimpleAuth, async (req, res) => {
    try {
      const userId = req.user.id;
      const query = req.query.q as string | undefined;
      const tag = req.query.tag as string | undefined;
      const folderId = req.query.folderId ? Number(req.query.folderId) : undefined;
      
      const recipes = await storage.searchRecipes(userId, query, tag, folderId);
      res.json(recipes);
    } catch (error) {
      res.status(500).json({ message: "Failed to search recipes" });
    }
  });
  
  return server;
}