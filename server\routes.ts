import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { setupAuth } from "./auth";
import { parseRecipeFromUrl } from "./recipeParser";
import { z } from "zod";
import { 
  insertRecipeSchema, 
  insertIngredientSchema, 
  insertStepSchema, 
  insertFolderSchema,
  insertFolderRecipeSchema,
  insertTagSchema,
  insertRecipeTagSchema,
  insertFavoriteSchema
} from "@shared/schema";

// Authentication middleware
function ensureAuthenticated(req: any, res: any, next: any) {
  if (req.isAuthenticated()) {
    return next();
  }
  res.status(401).json({ message: "Unauthorized" });
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Set up authentication routes
  setupAuth(app);

  // Demo login is handled in auth.ts
  
  // Add a demo user endpoint for direct access
  app.get("/api/demo-user", async (req, res) => {
    try {
      // Create or get the demo user
      let demoUser = await storage.getUserByUsername("demo_user");
      
      if (!demoUser) {
        demoUser = await storage.createUser({
          username: "demo_user",
          email: "<EMAIL>",
          password: "demo_password_hashed"
        });
      }
      
      res.status(200).json(demoUser);
    } catch (error) {
      res.status(500).json({ message: "Failed to get demo user" });
    }
  });
  
  // /api/user endpoint is defined in auth.ts
  
  // Recipe routes
  app.get("/api/recipes", async (req, res) => {
    try {
      // For demo mode or authenticated users
      const userId = req.isAuthenticated() ? req.user?.id : 1; // Demo user ID
      
      const recipes = await storage.getRecipesByUserId(userId);
      res.json(recipes);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch recipes" });
    }
  });

  app.get("/api/recipes/:id", async (req, res) => {
    try {
      console.log(`Fetching recipe details for ID: ${req.params.id}`);
      // For demo mode or authenticated users
      const userId = req.isAuthenticated() ? req.user?.id : 1; // Demo user ID
      const recipeId = parseInt(req.params.id);
      
      if (isNaN(recipeId)) {
        console.log(`Invalid recipe ID: ${req.params.id}`);
        return res.status(400).json({ message: "Invalid recipe ID" });
      }
      
      console.log(`Looking up recipe with ID: ${recipeId}`);
      const recipe = await storage.getRecipeById(recipeId);
      console.log(`Recipe lookup result:`, recipe ? "Found" : "Not found");
      
      if (!recipe) {
        return res.status(404).json({ message: "Recipe not found" });
      }
      
      // Skip permission check for demo mode (userId = 1)
      if (userId !== 1 && recipe.userId !== userId) {
        console.log(`Permission denied: user ${userId} cannot access recipe from user ${recipe.userId}`);
        return res.status(403).json({ message: "You don't have permission to view this recipe" });
      }
      
      // Get recipe details
      console.log(`Getting details for recipe ${recipeId}`);
      const ingredients = await storage.getIngredientsByRecipeId(recipeId);
      const steps = await storage.getStepsByRecipeId(recipeId);
      const tags = await storage.getTagsByRecipeId(recipeId);
      const isFavorite = await storage.isRecipeFavorite(userId, recipeId);
      
      console.log(`Recipe details: ${ingredients.length} ingredients, ${steps.length} steps, ${tags.length} tags`);
      
      const response = {
        recipe,
        ingredients,
        steps,
        tags,
        isFavorite
      };
      
      console.log(`Sending response for recipe ${recipeId}`);
      res.json(response);
    } catch (error) {
      console.error(`Error fetching recipe details: ${error}`);
      res.status(500).json({ message: "Failed to fetch recipe details" });
    }
  });

  app.post("/api/recipes", async (req, res) => {
    try {
      // Get user ID (from authenticated user or use demo user ID)
      const userId = req.isAuthenticated() ? req.user?.id : 1;
      
      // Validate recipe data
      const recipeData = {
        ...req.body.recipe,
        userId
      };
      
      console.log("Creating recipe:", recipeData.title);
      const recipe = await storage.createRecipe(recipeData);
      
      // Process ingredients
      if (Array.isArray(req.body.ingredients)) {
        for (const ingredient of req.body.ingredients) {
          await storage.createIngredient({
            ...ingredient,
            recipeId: recipe.id
          });
        }
      }
      
      // Process steps
      if (Array.isArray(req.body.steps)) {
        for (const step of req.body.steps) {
          await storage.createStep({
            ...step,
            recipeId: recipe.id
          });
        }
      }
      
      // Process tags
      if (Array.isArray(req.body.tags)) {
        for (const tagName of req.body.tags) {
          let tag = await storage.getTagByName(tagName);
          
          if (!tag) {
            tag = await storage.createTag({ name: tagName });
          }
          
          await storage.createRecipeTag({
            recipeId: recipe.id,
            tagId: tag.id
          });
        }
      }
      
      res.status(201).json({ recipeId: recipe.id });
    } catch (error) {
      console.error("Error creating recipe:", error);
      res.status(500).json({ message: "Failed to create recipe" });
    }
  });

  app.put("/api/recipes/:id", ensureAuthenticated, async (req, res) => {
    try {
      const userId = req.user?.id;
      const recipeId = parseInt(req.params.id);
      
      if (isNaN(recipeId)) {
        return res.status(400).json({ message: "Invalid recipe ID" });
      }
      
      const existingRecipe = await storage.getRecipeById(recipeId);
      
      if (!existingRecipe) {
        return res.status(404).json({ message: "Recipe not found" });
      }
      
      if (existingRecipe.userId !== userId) {
        return res.status(403).json({ message: "You don't have permission to update this recipe" });
      }
      
      // Update recipe
      await storage.updateRecipe(recipeId, req.body.recipe);
      
      // Delete existing ingredients and steps
      await storage.deleteIngredientsByRecipeId(recipeId);
      await storage.deleteStepsByRecipeId(recipeId);
      
      // Add new ingredients
      if (Array.isArray(req.body.ingredients)) {
        for (const ingredient of req.body.ingredients) {
          await storage.createIngredient({
            ...ingredient,
            recipeId
          });
        }
      }
      
      // Add new steps
      if (Array.isArray(req.body.steps)) {
        for (const step of req.body.steps) {
          await storage.createStep({
            ...step,
            recipeId
          });
        }
      }
      
      // Update tags
      if (Array.isArray(req.body.tags)) {
        await storage.deleteRecipeTagsByRecipeId(recipeId);
        
        for (const tagName of req.body.tags) {
          let tag = await storage.getTagByName(tagName);
          
          if (!tag) {
            tag = await storage.createTag({ name: tagName });
          }
          
          await storage.createRecipeTag({
            recipeId,
            tagId: tag.id
          });
        }
      }
      
      res.json({ message: "Recipe updated successfully" });
    } catch (error) {
      res.status(500).json({ message: "Failed to update recipe" });
    }
  });

  app.delete("/api/recipes/:id", ensureAuthenticated, async (req, res) => {
    try {
      const userId = req.user?.id;
      const recipeId = parseInt(req.params.id);
      
      if (isNaN(recipeId)) {
        return res.status(400).json({ message: "Invalid recipe ID" });
      }
      
      const recipe = await storage.getRecipeById(recipeId);
      
      if (!recipe) {
        return res.status(404).json({ message: "Recipe not found" });
      }
      
      if (recipe.userId !== userId) {
        return res.status(403).json({ message: "You don't have permission to delete this recipe" });
      }
      
      // Delete the recipe and all related data
      await storage.deleteRecipe(recipeId);
      
      res.json({ message: "Recipe deleted successfully" });
    } catch (error) {
      res.status(500).json({ message: "Failed to delete recipe" });
    }
  });

  // Recipe Import (no authentication required for demo)
  app.post("/api/recipes/import", async (req, res) => {
    try {
      // Get user ID (from authenticated user or use demo user ID)
      const userId = req.isAuthenticated() ? req.user?.id : 1;
      const { url } = req.body;
      
      if (!url) {
        return res.status(400).json({ message: "URL is required" });
      }
      
      console.log("Importing recipe from URL:", url);
      
      // First validate the URL format to give an early error if malformed
      try {
        new URL(url);
      } catch (e) {
        return res.status(400).json({ message: "Invalid URL format" });
      }
      
      // Show a more informative message while waiting for import
      res.setTimeout(60000); // Extend timeout to 60 seconds for AI parsing
      
      // Parse the recipe with our enhanced algorithm
      const parsedRecipe = await parseRecipeFromUrl(url);
      
      if (!parsedRecipe) {
        console.error("Failed to parse recipe from URL:", url);
        return res.status(400).json({ 
          message: "We couldn't import this recipe. This might be because the website doesn't have structured recipe data, or it's protected from being accessed programmatically. Try copying and pasting the recipe manually."
        });
      }
      
      // Check if we have enough data to consider this a successful import
      const hasMinimalData = parsedRecipe.recipe.title && 
                           (parsedRecipe.ingredients.length > 0 || 
                            parsedRecipe.steps.length > 0);
      
      if (!hasMinimalData) {
        console.log("Recipe parsed, but insufficient data was extracted");
        return res.status(400).json({ 
          message: "We could only extract minimal data from this recipe. Consider adding the details manually.",
          partialData: parsedRecipe
        });
      }
      
      console.log("Successfully parsed recipe:", parsedRecipe.recipe.title);
      console.log(`Extracted ${parsedRecipe.ingredients.length} ingredients and ${parsedRecipe.steps.length} steps`);
      
      res.json(parsedRecipe);
    } catch (error) {
      console.error("Recipe import error:", error);
      res.status(500).json({ 
        message: "Failed to import recipe. Our system encountered a problem processing this request. Please try again later."
      });
    }
  });

  // Folder routes
  app.get("/api/folders", async (req, res) => {
    try {
      // For demo mode or authenticated users
      const userId = req.isAuthenticated() ? req.user?.id : 1; // Demo user ID
      
      const folders = await storage.getFoldersByUserId(userId);
      
      // Get recipe counts for each folder
      const foldersWithCounts = await Promise.all(
        folders.map(async (folder) => {
          const count = await storage.getRecipeCountByFolderId(folder.id);
          return {
            ...folder,
            recipeCount: count
          };
        })
      );
      
      res.json(foldersWithCounts);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch folders" });
    }
  });

  app.post("/api/folders", ensureAuthenticated, async (req, res) => {
    try {
      const userId = req.user?.id;
      const folderData = {
        ...req.body,
        userId
      };
      
      const folder = await storage.createFolder(folderData);
      res.status(201).json(folder);
    } catch (error) {
      res.status(500).json({ message: "Failed to create folder" });
    }
  });

  app.put("/api/folders/:id", ensureAuthenticated, async (req, res) => {
    try {
      const userId = req.user?.id;
      const folderId = parseInt(req.params.id);
      
      if (isNaN(folderId)) {
        return res.status(400).json({ message: "Invalid folder ID" });
      }
      
      const folder = await storage.getFolderById(folderId);
      
      if (!folder) {
        return res.status(404).json({ message: "Folder not found" });
      }
      
      if (folder.userId !== userId) {
        return res.status(403).json({ message: "You don't have permission to update this folder" });
      }
      
      await storage.updateFolder(folderId, req.body);
      res.json({ message: "Folder updated successfully" });
    } catch (error) {
      res.status(500).json({ message: "Failed to update folder" });
    }
  });

  app.delete("/api/folders/:id", ensureAuthenticated, async (req, res) => {
    try {
      const userId = req.user?.id;
      const folderId = parseInt(req.params.id);
      
      if (isNaN(folderId)) {
        return res.status(400).json({ message: "Invalid folder ID" });
      }
      
      const folder = await storage.getFolderById(folderId);
      
      if (!folder) {
        return res.status(404).json({ message: "Folder not found" });
      }
      
      if (folder.userId !== userId) {
        return res.status(403).json({ message: "You don't have permission to delete this folder" });
      }
      
      await storage.deleteFolder(folderId);
      res.json({ message: "Folder deleted successfully" });
    } catch (error) {
      res.status(500).json({ message: "Failed to delete folder" });
    }
  });

  // Folder-Recipe associations
  app.post("/api/folders/:folderId/recipes/:recipeId", ensureAuthenticated, async (req, res) => {
    try {
      const userId = req.user?.id;
      const folderId = parseInt(req.params.folderId);
      const recipeId = parseInt(req.params.recipeId);
      
      if (isNaN(folderId) || isNaN(recipeId)) {
        return res.status(400).json({ message: "Invalid folder or recipe ID" });
      }
      
      const folder = await storage.getFolderById(folderId);
      const recipe = await storage.getRecipeById(recipeId);
      
      if (!folder || !recipe) {
        return res.status(404).json({ message: "Folder or recipe not found" });
      }
      
      if (folder.userId !== userId || recipe.userId !== userId) {
        return res.status(403).json({ message: "You don't have permission to perform this action" });
      }
      
      await storage.addRecipeToFolder({ folderId, recipeId });
      res.json({ message: "Recipe added to folder successfully" });
    } catch (error) {
      res.status(500).json({ message: "Failed to add recipe to folder" });
    }
  });

  app.delete("/api/folders/:folderId/recipes/:recipeId", ensureAuthenticated, async (req, res) => {
    try {
      const userId = req.user?.id;
      const folderId = parseInt(req.params.folderId);
      const recipeId = parseInt(req.params.recipeId);
      
      if (isNaN(folderId) || isNaN(recipeId)) {
        return res.status(400).json({ message: "Invalid folder or recipe ID" });
      }
      
      const folder = await storage.getFolderById(folderId);
      const recipe = await storage.getRecipeById(recipeId);
      
      if (!folder || !recipe) {
        return res.status(404).json({ message: "Folder or recipe not found" });
      }
      
      if (folder.userId !== userId || recipe.userId !== userId) {
        return res.status(403).json({ message: "You don't have permission to perform this action" });
      }
      
      await storage.removeRecipeFromFolder(folderId, recipeId);
      res.json({ message: "Recipe removed from folder successfully" });
    } catch (error) {
      res.status(500).json({ message: "Failed to remove recipe from folder" });
    }
  });

  // Favorites
  app.post("/api/favorites/:recipeId", ensureAuthenticated, async (req, res) => {
    try {
      const userId = req.user?.id;
      const recipeId = parseInt(req.params.recipeId);
      
      if (isNaN(recipeId)) {
        return res.status(400).json({ message: "Invalid recipe ID" });
      }
      
      const recipe = await storage.getRecipeById(recipeId);
      
      if (!recipe) {
        return res.status(404).json({ message: "Recipe not found" });
      }
      
      if (recipe.userId !== userId) {
        return res.status(403).json({ message: "You don't have permission to favorite this recipe" });
      }
      
      await storage.addFavorite({ userId, recipeId });
      res.json({ message: "Recipe added to favorites successfully" });
    } catch (error) {
      res.status(500).json({ message: "Failed to add recipe to favorites" });
    }
  });

  app.delete("/api/favorites/:recipeId", async (req, res) => {
    try {
      // Get user ID (from authenticated user or use demo user ID)
      const userId = req.isAuthenticated() ? req.user?.id : 1;
      const recipeId = parseInt(req.params.recipeId);
      
      if (isNaN(recipeId)) {
        return res.status(400).json({ message: "Invalid recipe ID" });
      }
      
      // Make sure userId is a number before passing to storage
      if (userId) {
        await storage.removeFavorite(userId, recipeId);
        res.json({ message: "Recipe removed from favorites successfully" });
      } else {
        res.status(401).json({ message: "User not authenticated" });
      }
    } catch (error) {
      console.error("Error removing favorite:", error);
      res.status(500).json({ message: "Failed to remove recipe from favorites" });
    }
  });

  app.get("/api/favorites", async (req, res) => {
    try {
      // For demo mode or authenticated users
      const userId = req.isAuthenticated() ? req.user?.id : 1; // Demo user ID
      
      if (!userId) {
        return res.status(401).json({ message: "Unauthorized" });
      }
      
      const favorites = await storage.getFavoritesByUserId(userId);
      res.json(favorites);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch favorites" });
    }
  });

  // Search & Filter (no authentication required for demo)
  app.get("/api/search", async (req, res) => {
    try {
      // Get user ID (from authenticated user or use demo user ID)
      const userId = req.isAuthenticated() ? req.user?.id : 1;
      const query = req.query.q as string || '';
      const tag = req.query.tag as string || '';
      const folderStr = req.query.folder as string | undefined;
      
      // Convert folder to number if present, or use null (not undefined)
      const folder = folderStr ? parseInt(folderStr) : null;
      
      console.log("Searching recipes:", { query, tag, folder });
      
      if (userId) {
        // For TypeScript, passing null is safer than undefined for optional parameters
        const recipes = await storage.searchRecipes(userId, query, tag, folder || undefined);
        res.json(recipes);
      } else {
        res.status(401).json({ message: "User not authenticated" });
      }
    } catch (error) {
      console.error("Search error:", error);
      res.status(500).json({ message: "Failed to search recipes" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
