import { 
  users, type User, type InsertUser,
  recipes, type Recipe, type InsertRecipe,
  ingredients, type Ingredient, type InsertIngredient,
  steps, type Step, type InsertStep,
  folders, type Folder, type InsertFolder,
  folderRecipes, type FolderRecipe, type InsertFolderRecipe,
  tags, type Tag, type InsertTag,
  recipeTags, type RecipeTag, type InsertRecipeTag,
  favorites, type Favorite, type InsertFavorite
} from "@shared/schema";
import session from "express-session";
import createMemoryStore from "memorystore";
import connectPg from "connect-pg-simple";
import { eq, and, or, like, desc } from "drizzle-orm";
import { db, pool } from "./db";

const MemoryStore = createMemoryStore(session);
const PostgresSessionStore = connectPg(session);

export interface IStorage {
  // User
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Recipe
  getRecipeById(id: number): Promise<Recipe | undefined>;
  getRecipesByUserId(userId: number): Promise<Recipe[]>;
  createRecipe(recipe: InsertRecipe): Promise<Recipe>;
  updateRecipe(id: number, recipe: Partial<InsertRecipe>): Promise<void>;
  deleteRecipe(id: number): Promise<void>;
  
  // Ingredient
  getIngredientsByRecipeId(recipeId: number): Promise<Ingredient[]>;
  createIngredient(ingredient: InsertIngredient): Promise<Ingredient>;
  deleteIngredientsByRecipeId(recipeId: number): Promise<void>;
  
  // Step
  getStepsByRecipeId(recipeId: number): Promise<Step[]>;
  createStep(step: InsertStep): Promise<Step>;
  deleteStepsByRecipeId(recipeId: number): Promise<void>;
  
  // Folder
  getFolderById(id: number): Promise<Folder | undefined>;
  getFoldersByUserId(userId: number): Promise<Folder[]>;
  createFolder(folder: InsertFolder): Promise<Folder>;
  updateFolder(id: number, folder: Partial<InsertFolder>): Promise<void>;
  deleteFolder(id: number): Promise<void>;
  getRecipeCountByFolderId(folderId: number): Promise<number>;
  
  // Folder-Recipe
  getRecipesByFolderId(folderId: number): Promise<Recipe[]>;
  addRecipeToFolder(folderRecipe: InsertFolderRecipe): Promise<void>;
  removeRecipeFromFolder(folderId: number, recipeId: number): Promise<void>;
  
  // Tag
  getTagById(id: number): Promise<Tag | undefined>;
  getTagByName(name: string): Promise<Tag | undefined>;
  createTag(tag: InsertTag): Promise<Tag>;
  getTagsByRecipeId(recipeId: number): Promise<Tag[]>;
  
  // Recipe-Tag
  createRecipeTag(recipeTag: InsertRecipeTag): Promise<void>;
  deleteRecipeTagsByRecipeId(recipeId: number): Promise<void>;
  
  // Favorite
  getFavoritesByUserId(userId: number): Promise<Recipe[]>;
  addFavorite(favorite: InsertFavorite): Promise<void>;
  removeFavorite(userId: number, recipeId: number): Promise<void>;
  isRecipeFavorite(userId: number, recipeId: number): Promise<boolean>;
  
  // Search
  searchRecipes(userId: number, query?: string, tag?: string, folderId?: number): Promise<Recipe[]>;
  
  // Session
  sessionStore: session.Store;
}

export class MemStorage implements IStorage {
  private usersMap: Map<number, User>;
  private recipesMap: Map<number, Recipe>;
  private ingredientsMap: Map<number, Ingredient>;
  private stepsMap: Map<number, Step>;
  private foldersMap: Map<number, Folder>;
  private folderRecipesMap: Map<string, FolderRecipe>;
  private tagsMap: Map<number, Tag>;
  private recipeTagsMap: Map<string, RecipeTag>;
  private favoritesMap: Map<string, Favorite>;
  
  sessionStore: session.Store;
  
  private userIdCounter: number;
  private recipeIdCounter: number;
  private ingredientIdCounter: number;
  private stepIdCounter: number;
  private folderIdCounter: number;
  private tagIdCounter: number;
  
  constructor() {
    this.usersMap = new Map();
    this.recipesMap = new Map();
    this.ingredientsMap = new Map();
    this.stepsMap = new Map();
    this.foldersMap = new Map();
    this.folderRecipesMap = new Map();
    this.tagsMap = new Map();
    this.recipeTagsMap = new Map();
    this.favoritesMap = new Map();
    
    this.userIdCounter = 1;
    this.recipeIdCounter = 1;
    this.ingredientIdCounter = 1;
    this.stepIdCounter = 1;
    this.folderIdCounter = 1;
    this.tagIdCounter = 1;
    
    this.sessionStore = new MemoryStore({
      checkPeriod: 86400000, // Clear expired sessions every day
    });
  }
  
  // User methods
  async getUser(id: number): Promise<User | undefined> {
    return this.usersMap.get(id);
  }
  
  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.usersMap.values()).find(
      (user) => user.username.toLowerCase() === username.toLowerCase()
    );
  }
  
  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.userIdCounter++;
    const user: User = { ...insertUser, id };
    this.usersMap.set(id, user);
    return user;
  }
  
  // Recipe methods
  async getRecipeById(id: number): Promise<Recipe | undefined> {
    return this.recipesMap.get(id);
  }
  
  async getRecipesByUserId(userId: number): Promise<Recipe[]> {
    return Array.from(this.recipesMap.values()).filter(
      (recipe) => recipe.userId === userId
    );
  }
  
  async createRecipe(insertRecipe: InsertRecipe): Promise<Recipe> {
    const id = this.recipeIdCounter++;
    const now = new Date();
    const recipe: Recipe = { 
      ...insertRecipe, 
      id, 
      createdAt: now, 
      updatedAt: now 
    };
    this.recipesMap.set(id, recipe);
    return recipe;
  }
  
  async updateRecipe(id: number, recipeUpdate: Partial<InsertRecipe>): Promise<void> {
    const recipe = this.recipesMap.get(id);
    if (!recipe) return;
    
    const updatedRecipe: Recipe = { 
      ...recipe, 
      ...recipeUpdate, 
      updatedAt: new Date() 
    };
    this.recipesMap.set(id, updatedRecipe);
  }
  
  async deleteRecipe(id: number): Promise<void> {
    // Delete recipe
    this.recipesMap.delete(id);
    
    // Delete associated ingredients
    for (const [ingredientId, ingredient] of this.ingredientsMap.entries()) {
      if (ingredient.recipeId === id) {
        this.ingredientsMap.delete(ingredientId);
      }
    }
    
    // Delete associated steps
    for (const [stepId, step] of this.stepsMap.entries()) {
      if (step.recipeId === id) {
        this.stepsMap.delete(stepId);
      }
    }
    
    // Delete folder associations
    for (const [key, folderRecipe] of this.folderRecipesMap.entries()) {
      if (folderRecipe.recipeId === id) {
        this.folderRecipesMap.delete(key);
      }
    }
    
    // Delete tag associations
    for (const [key, recipeTag] of this.recipeTagsMap.entries()) {
      if (recipeTag.recipeId === id) {
        this.recipeTagsMap.delete(key);
      }
    }
    
    // Delete favorites
    for (const [key, favorite] of this.favoritesMap.entries()) {
      if (favorite.recipeId === id) {
        this.favoritesMap.delete(key);
      }
    }
  }
  
  // Ingredient methods
  async getIngredientsByRecipeId(recipeId: number): Promise<Ingredient[]> {
    return Array.from(this.ingredientsMap.values()).filter(
      (ingredient) => ingredient.recipeId === recipeId
    );
  }
  
  async createIngredient(insertIngredient: InsertIngredient): Promise<Ingredient> {
    const id = this.ingredientIdCounter++;
    const ingredient: Ingredient = { ...insertIngredient, id };
    this.ingredientsMap.set(id, ingredient);
    return ingredient;
  }
  
  async deleteIngredientsByRecipeId(recipeId: number): Promise<void> {
    for (const [id, ingredient] of this.ingredientsMap.entries()) {
      if (ingredient.recipeId === recipeId) {
        this.ingredientsMap.delete(id);
      }
    }
  }
  
  // Step methods
  async getStepsByRecipeId(recipeId: number): Promise<Step[]> {
    return Array.from(this.stepsMap.values())
      .filter((step) => step.recipeId === recipeId)
      .sort((a, b) => a.stepNumber - b.stepNumber);
  }
  
  async createStep(insertStep: InsertStep): Promise<Step> {
    const id = this.stepIdCounter++;
    const step: Step = { ...insertStep, id };
    this.stepsMap.set(id, step);
    return step;
  }
  
  async deleteStepsByRecipeId(recipeId: number): Promise<void> {
    for (const [id, step] of this.stepsMap.entries()) {
      if (step.recipeId === recipeId) {
        this.stepsMap.delete(id);
      }
    }
  }
  
  // Folder methods
  async getFolderById(id: number): Promise<Folder | undefined> {
    return this.foldersMap.get(id);
  }
  
  async getFoldersByUserId(userId: number): Promise<Folder[]> {
    return Array.from(this.foldersMap.values()).filter(
      (folder) => folder.userId === userId
    );
  }
  
  async createFolder(insertFolder: InsertFolder): Promise<Folder> {
    const id = this.folderIdCounter++;
    const folder: Folder = { ...insertFolder, id };
    this.foldersMap.set(id, folder);
    return folder;
  }
  
  async updateFolder(id: number, folderUpdate: Partial<InsertFolder>): Promise<void> {
    const folder = this.foldersMap.get(id);
    if (!folder) return;
    
    const updatedFolder: Folder = { ...folder, ...folderUpdate };
    this.foldersMap.set(id, updatedFolder);
  }
  
  async deleteFolder(id: number): Promise<void> {
    // Delete folder
    this.foldersMap.delete(id);
    
    // Delete folder-recipe associations
    for (const [key, folderRecipe] of this.folderRecipesMap.entries()) {
      if (folderRecipe.folderId === id) {
        this.folderRecipesMap.delete(key);
      }
    }
  }
  
  async getRecipeCountByFolderId(folderId: number): Promise<number> {
    return Array.from(this.folderRecipesMap.values()).filter(
      (fr) => fr.folderId === folderId
    ).length;
  }
  
  // Folder-Recipe methods
  async getRecipesByFolderId(folderId: number): Promise<Recipe[]> {
    const recipeIds = Array.from(this.folderRecipesMap.values())
      .filter((fr) => fr.folderId === folderId)
      .map((fr) => fr.recipeId);
    
    return Array.from(this.recipesMap.values()).filter(
      (recipe) => recipeIds.includes(recipe.id)
    );
  }
  
  async addRecipeToFolder(insertFolderRecipe: InsertFolderRecipe): Promise<void> {
    const key = `${insertFolderRecipe.folderId}-${insertFolderRecipe.recipeId}`;
    this.folderRecipesMap.set(key, insertFolderRecipe);
  }
  
  async removeRecipeFromFolder(folderId: number, recipeId: number): Promise<void> {
    const key = `${folderId}-${recipeId}`;
    this.folderRecipesMap.delete(key);
  }
  
  // Tag methods
  async getTagById(id: number): Promise<Tag | undefined> {
    return this.tagsMap.get(id);
  }
  
  async getTagByName(name: string): Promise<Tag | undefined> {
    return Array.from(this.tagsMap.values()).find(
      (tag) => tag.name.toLowerCase() === name.toLowerCase()
    );
  }
  
  async createTag(insertTag: InsertTag): Promise<Tag> {
    const id = this.tagIdCounter++;
    const tag: Tag = { ...insertTag, id };
    this.tagsMap.set(id, tag);
    return tag;
  }
  
  async getTagsByRecipeId(recipeId: number): Promise<Tag[]> {
    const tagIds = Array.from(this.recipeTagsMap.values())
      .filter((rt) => rt.recipeId === recipeId)
      .map((rt) => rt.tagId);
    
    return Array.from(this.tagsMap.values()).filter(
      (tag) => tagIds.includes(tag.id)
    );
  }
  
  // Recipe-Tag methods
  async createRecipeTag(insertRecipeTag: InsertRecipeTag): Promise<void> {
    const key = `${insertRecipeTag.recipeId}-${insertRecipeTag.tagId}`;
    this.recipeTagsMap.set(key, insertRecipeTag);
  }
  
  async deleteRecipeTagsByRecipeId(recipeId: number): Promise<void> {
    for (const [key, recipeTag] of this.recipeTagsMap.entries()) {
      if (recipeTag.recipeId === recipeId) {
        this.recipeTagsMap.delete(key);
      }
    }
  }
  
  // Favorite methods
  async getFavoritesByUserId(userId: number): Promise<Recipe[]> {
    const recipeIds = Array.from(this.favoritesMap.values())
      .filter((fav) => fav.userId === userId)
      .map((fav) => fav.recipeId);
    
    return Array.from(this.recipesMap.values()).filter(
      (recipe) => recipeIds.includes(recipe.id)
    );
  }
  
  async addFavorite(insertFavorite: InsertFavorite): Promise<void> {
    const key = `${insertFavorite.userId}-${insertFavorite.recipeId}`;
    this.favoritesMap.set(key, insertFavorite);
  }
  
  async removeFavorite(userId: number, recipeId: number): Promise<void> {
    const key = `${userId}-${recipeId}`;
    this.favoritesMap.delete(key);
  }
  
  async isRecipeFavorite(userId: number, recipeId: number): Promise<boolean> {
    const key = `${userId}-${recipeId}`;
    return this.favoritesMap.has(key);
  }
  
  // Search
  async searchRecipes(userId: number, query?: string, tag?: string, folderId?: number): Promise<Recipe[]> {
    let recipes = Array.from(this.recipesMap.values()).filter(
      (recipe) => recipe.userId === userId
    );
    
    // Filter by folder if provided
    if (folderId) {
      const recipeIds = Array.from(this.folderRecipesMap.values())
        .filter((fr) => fr.folderId === folderId)
        .map((fr) => fr.recipeId);
      
      recipes = recipes.filter((recipe) => recipeIds.includes(recipe.id));
    }
    
    // Filter by tag if provided
    if (tag) {
      const tagObj = await this.getTagByName(tag);
      
      if (tagObj) {
        const taggedRecipeIds = Array.from(this.recipeTagsMap.values())
          .filter((rt) => rt.tagId === tagObj.id)
          .map((rt) => rt.recipeId);
        
        recipes = recipes.filter((recipe) => taggedRecipeIds.includes(recipe.id));
      } else {
        return []; // No recipes with non-existent tag
      }
    }
    
    // Search by query if provided
    if (query) {
      const lowerQuery = query.toLowerCase();
      recipes = recipes.filter((recipe) => {
        const titleMatch = recipe.title.toLowerCase().includes(lowerQuery);
        const descMatch = recipe.description?.toLowerCase().includes(lowerQuery) || false;
        
        return titleMatch || descMatch;
      });
    }
    
    return recipes;
  }
}

export class DatabaseStorage implements IStorage {
  sessionStore: session.Store;

  constructor() {
    this.sessionStore = new PostgresSessionStore({ 
      pool, 
      createTableIfMissing: true 
    });
  }

  // User methods
  async getUser(id: number): Promise<User | undefined> {
    try {
      const result = await db.select().from(users).where(eq(users.id, id));
      return result[0];
    } catch (error) {
      console.error('Error getting user by ID:', error);
      return undefined;
    }
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    try {
      const result = await db.select().from(users).where(eq(users.username, username));
      return result[0];
    } catch (error) {
      console.error('Error getting user by username:', error);
      return undefined;
    }
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    try {
      const result = await db.insert(users).values(insertUser).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  // Recipe methods
  async getRecipeById(id: number): Promise<Recipe | undefined> {
    try {
      const result = await db.select().from(recipes).where(eq(recipes.id, id));
      return result[0];
    } catch (error) {
      console.error('Error getting recipe by ID:', error);
      return undefined;
    }
  }

  async getRecipesByUserId(userId: number): Promise<Recipe[]> {
    try {
      return await db.select().from(recipes).where(eq(recipes.userId, userId));
    } catch (error) {
      console.error('Error getting recipes by user ID:', error);
      return [];
    }
  }

  async createRecipe(insertRecipe: InsertRecipe): Promise<Recipe> {
    try {
      const [recipe] = await db.insert(recipes).values(insertRecipe).returning();
      return recipe;
    } catch (error) {
      console.error('Error creating recipe:', error);
      throw error;
    }
  }

  async updateRecipe(id: number, recipeUpdate: Partial<InsertRecipe>): Promise<void> {
    try {
      await db.update(recipes).set(recipeUpdate).where(eq(recipes.id, id));
    } catch (error) {
      console.error('Error updating recipe:', error);
      throw error;
    }
  }

  async deleteRecipe(id: number): Promise<void> {
    try {
      // Delete recipe tags
      await db.delete(recipeTags).where(eq(recipeTags.recipeId, id));
      
      // Delete recipe steps
      await db.delete(steps).where(eq(steps.recipeId, id));
      
      // Delete recipe ingredients
      await db.delete(ingredients).where(eq(ingredients.recipeId, id));
      
      // Delete folder-recipe associations
      await db.delete(folderRecipes).where(eq(folderRecipes.recipeId, id));
      
      // Delete recipe favorites
      await db.delete(favorites).where(eq(favorites.recipeId, id));
      
      // Delete the recipe itself
      await db.delete(recipes).where(eq(recipes.id, id));
    } catch (error) {
      console.error('Error deleting recipe:', error);
      throw error;
    }
  }

  // Ingredient methods
  async getIngredientsByRecipeId(recipeId: number): Promise<Ingredient[]> {
    try {
      return await db.select().from(ingredients).where(eq(ingredients.recipeId, recipeId));
    } catch (error) {
      console.error('Error getting ingredients by recipe ID:', error);
      return [];
    }
  }

  async createIngredient(insertIngredient: InsertIngredient): Promise<Ingredient> {
    try {
      const [ingredient] = await db.insert(ingredients).values(insertIngredient).returning();
      return ingredient;
    } catch (error) {
      console.error('Error creating ingredient:', error);
      throw error;
    }
  }

  async deleteIngredientsByRecipeId(recipeId: number): Promise<void> {
    try {
      await db.delete(ingredients).where(eq(ingredients.recipeId, recipeId));
    } catch (error) {
      console.error('Error deleting ingredients by recipe ID:', error);
      throw error;
    }
  }

  // Step methods
  async getStepsByRecipeId(recipeId: number): Promise<Step[]> {
    try {
      return await db.select().from(steps).where(eq(steps.recipeId, recipeId)).orderBy(steps.stepNumber);
    } catch (error) {
      console.error('Error getting steps by recipe ID:', error);
      return [];
    }
  }

  async createStep(insertStep: InsertStep): Promise<Step> {
    try {
      const [step] = await db.insert(steps).values(insertStep).returning();
      return step;
    } catch (error) {
      console.error('Error creating step:', error);
      throw error;
    }
  }

  async deleteStepsByRecipeId(recipeId: number): Promise<void> {
    try {
      await db.delete(steps).where(eq(steps.recipeId, recipeId));
    } catch (error) {
      console.error('Error deleting steps by recipe ID:', error);
      throw error;
    }
  }

  // Folder methods
  async getFolderById(id: number): Promise<Folder | undefined> {
    try {
      const [folder] = await db.select().from(folders).where(eq(folders.id, id));
      return folder;
    } catch (error) {
      console.error('Error getting folder by ID:', error);
      return undefined;
    }
  }

  async getFoldersByUserId(userId: number): Promise<Folder[]> {
    try {
      return await db.select().from(folders).where(eq(folders.userId, userId));
    } catch (error) {
      console.error('Error getting folders by user ID:', error);
      return [];
    }
  }

  async createFolder(insertFolder: InsertFolder): Promise<Folder> {
    try {
      const [folder] = await db.insert(folders).values(insertFolder).returning();
      return folder;
    } catch (error) {
      console.error('Error creating folder:', error);
      throw error;
    }
  }

  async updateFolder(id: number, folderUpdate: Partial<InsertFolder>): Promise<void> {
    try {
      await db.update(folders).set(folderUpdate).where(eq(folders.id, id));
    } catch (error) {
      console.error('Error updating folder:', error);
      throw error;
    }
  }

  async deleteFolder(id: number): Promise<void> {
    try {
      // Delete folder-recipe associations
      await db.delete(folderRecipes).where(eq(folderRecipes.folderId, id));
      
      // Delete the folder itself
      await db.delete(folders).where(eq(folders.id, id));
    } catch (error) {
      console.error('Error deleting folder:', error);
      throw error;
    }
  }

  async getRecipeCountByFolderId(folderId: number): Promise<number> {
    try {
      // Use pool directly for raw SQL queries
      const result = await pool.query(
        `SELECT COUNT(*) FROM folder_recipes WHERE folder_id = $1`,
        [folderId]
      );
      return Number(result.rows[0].count) || 0;
    } catch (error) {
      console.error('Error getting recipe count by folder ID:', error);
      return 0;
    }
  }

  // Folder-Recipe methods
  async getRecipesByFolderId(folderId: number): Promise<Recipe[]> {
    try {
      const result = await db
        .select({
          recipe: recipes
        })
        .from(folderRecipes)
        .innerJoin(recipes, eq(recipes.id, folderRecipes.recipeId))
        .where(eq(folderRecipes.folderId, folderId));
      
      return result.map(r => r.recipe);
    } catch (error) {
      console.error('Error getting recipes by folder ID:', error);
      return [];
    }
  }

  async addRecipeToFolder(folderRecipe: InsertFolderRecipe): Promise<void> {
    try {
      await db.insert(folderRecipes).values(folderRecipe);
    } catch (error) {
      console.error('Error adding recipe to folder:', error);
      throw error;
    }
  }

  async removeRecipeFromFolder(folderId: number, recipeId: number): Promise<void> {
    try {
      await db.delete(folderRecipes)
        .where(
          and(
            eq(folderRecipes.folderId, folderId),
            eq(folderRecipes.recipeId, recipeId)
          )
        );
    } catch (error) {
      console.error('Error removing recipe from folder:', error);
      throw error;
    }
  }

  // Tag methods
  async getTagById(id: number): Promise<Tag | undefined> {
    try {
      const [tag] = await db.select().from(tags).where(eq(tags.id, id));
      return tag;
    } catch (error) {
      console.error('Error getting tag by ID:', error);
      return undefined;
    }
  }

  async getTagByName(name: string): Promise<Tag | undefined> {
    try {
      const [tag] = await db.select().from(tags).where(eq(tags.name, name));
      return tag;
    } catch (error) {
      console.error('Error getting tag by name:', error);
      return undefined;
    }
  }

  async createTag(insertTag: InsertTag): Promise<Tag> {
    try {
      const [tag] = await db.insert(tags).values(insertTag).returning();
      return tag;
    } catch (error) {
      console.error('Error creating tag:', error);
      throw error;
    }
  }

  async getTagsByRecipeId(recipeId: number): Promise<Tag[]> {
    try {
      const result = await db
        .select({
          tag: tags
        })
        .from(recipeTags)
        .innerJoin(tags, eq(tags.id, recipeTags.tagId))
        .where(eq(recipeTags.recipeId, recipeId));
      
      return result.map(r => r.tag);
    } catch (error) {
      console.error('Error getting tags by recipe ID:', error);
      return [];
    }
  }

  // Recipe-Tag methods
  async createRecipeTag(recipeTag: InsertRecipeTag): Promise<void> {
    try {
      await db.insert(recipeTags).values(recipeTag);
    } catch (error) {
      console.error('Error creating recipe tag:', error);
      throw error;
    }
  }

  async deleteRecipeTagsByRecipeId(recipeId: number): Promise<void> {
    try {
      await db.delete(recipeTags).where(eq(recipeTags.recipeId, recipeId));
    } catch (error) {
      console.error('Error deleting recipe tags by recipe ID:', error);
      throw error;
    }
  }

  // Favorite methods
  async getFavoritesByUserId(userId: number): Promise<Recipe[]> {
    try {
      const result = await db
        .select({
          recipe: recipes
        })
        .from(favorites)
        .innerJoin(recipes, eq(recipes.id, favorites.recipeId))
        .where(eq(favorites.userId, userId));
      
      return result.map(r => r.recipe);
    } catch (error) {
      console.error('Error getting favorites by user ID:', error);
      return [];
    }
  }

  async addFavorite(favorite: InsertFavorite): Promise<void> {
    try {
      await db.insert(favorites).values(favorite);
    } catch (error) {
      console.error('Error adding favorite:', error);
      throw error;
    }
  }

  async removeFavorite(userId: number, recipeId: number): Promise<void> {
    try {
      await db.delete(favorites)
        .where(
          and(
            eq(favorites.userId, userId),
            eq(favorites.recipeId, recipeId)
          )
        );
    } catch (error) {
      console.error('Error removing favorite:', error);
      throw error;
    }
  }

  async isRecipeFavorite(userId: number, recipeId: number): Promise<boolean> {
    try {
      const result = await pool.query(
        `SELECT COUNT(*) FROM favorites WHERE user_id = $1 AND recipe_id = $2`,
        [userId, recipeId]
      );
      
      return Number(result.rows[0].count) > 0;
    } catch (error) {
      console.error('Error checking if recipe is favorite:', error);
      return false;
    }
  }

  // Search methods
  async searchRecipes(userId: number, query?: string, tag?: string, folderId?: number): Promise<Recipe[]> {
    try {
      let sql = `SELECT * FROM recipes WHERE user_id = $1`;
      const params: any[] = [userId];
      let paramIndex = 2;
      
      // Filter by search query
      if (query && query.trim().length > 0) {
        sql += ` AND (title ILIKE $${paramIndex} OR description ILIKE $${paramIndex})`;
        params.push(`%${query}%`);
        paramIndex++;
      }
      
      // Filter by tag
      if (tag && tag.trim().length > 0) {
        // Get recipe IDs with this tag
        const tagQuery = await pool.query(
          `SELECT rt.recipe_id FROM recipe_tags rt 
           JOIN tags t ON rt.tag_id = t.id 
           WHERE t.name = $1`,
          [tag]
        );
        
        if (tagQuery.rows.length === 0) {
          return []; // No recipes have this tag
        }
        
        const recipeIds = tagQuery.rows.map(row => row.recipe_id);
        sql += ` AND id = ANY($${paramIndex})`;
        params.push(recipeIds);
        paramIndex++;
      }
      
      // Filter by folder
      if (folderId !== undefined && folderId !== null) {
        // Get recipe IDs in this folder
        const folderQuery = await pool.query(
          `SELECT recipe_id FROM folder_recipes WHERE folder_id = $1`,
          [folderId]
        );
        
        if (folderQuery.rows.length === 0) {
          return []; // No recipes in this folder
        }
        
        const recipeIds = folderQuery.rows.map(row => row.recipe_id);
        sql += ` AND id = ANY($${paramIndex})`;
        params.push(recipeIds);
        paramIndex++;
      }
      
      // Order by newest first
      sql += ` ORDER BY created_at DESC`;
      
      const result = await pool.query(sql, params);
      return result.rows;
    } catch (error) {
      console.error('Error searching recipes:', error);
      return [];
    }
  }
}

// export const storage = new MemStorage();
export const storage = new DatabaseStorage();
