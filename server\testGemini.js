// Simple script to test Gemini API connectivity
import { GoogleGenerativeAI } from "@google/generative-ai";

async function testGemini() {
  try {
    console.log("Testing Gemini API...");
    console.log("API Key format check:", process.env.GEMINI_API_KEY ? "✅ Key exists" : "❌ No key found");
    
    // Initialize the API client
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    
    // List available models
    console.log("Attempting to list available models...");
    
    // Try both gemini-pro and gemini-1.0-pro models
    // Try with gemini-1.0-pro first (newer version)
    try {
      console.log("Testing with gemini-1.0-pro...");
      const model = genAI.getGenerativeModel({ model: "gemini-1.0-pro" });
      const result = await model.generateContent("Hello, what time is it?");
      const response = await result.response;
      console.log("✅ Success with gemini-1.0-pro!");
      console.log("Response:", response.text());
      return "gemini-1.0-pro";
    } catch (error1) {
      console.log("❌ Failed with gemini-1.0-pro:", error1.message);
      
      // Try with gemini-pro (older version)
      try {
        console.log("Testing with gemini-pro...");
        const model = genAI.getGenerativeModel({ model: "gemini-pro" });
        const result = await model.generateContent("Hello, what time is it?");
        const response = await result.response;
        console.log("✅ Success with gemini-pro!");
        console.log("Response:", response.text());
        return "gemini-pro";
      } catch (error2) {
        console.log("❌ Failed with gemini-pro:", error2.message);
        throw new Error("Both model versions failed");
      }
    }
  } catch (error) {
    console.error("❌ Gemini API test failed:", error.message);
    if (error.message.includes("API key")) {
      console.log("This might be an API key issue. Please check your API key is valid.");
    }
    return null;
  }
}

testGemini()
  .then(model => {
    if (model) {
      console.log(`✅ Gemini test completed successfully using model: ${model}`);
    } else {
      console.log("❌ Gemini test failed");
    }
  })
  .catch(err => {
    console.error("Test error:", err);
  });