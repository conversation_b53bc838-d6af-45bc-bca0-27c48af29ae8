import { pgTable, text, serial, integer, boolean, timestamp, primaryKey } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  email: text("email").notNull().unique(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
  email: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

export const recipes = pgTable("recipes", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id),
  title: text("title").notNull(),
  description: text("description"),
  imageUrl: text("image_url"),
  prepTime: text("prep_time"),
  cookTime: text("cook_time"),
  sourceUrl: text("source_url"),
  notes: text("notes"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const insertRecipeSchema = createInsertSchema(recipes).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export type InsertRecipe = z.infer<typeof insertRecipeSchema>;
export type Recipe = typeof recipes.$inferSelect;

export const ingredients = pgTable("ingredients", {
  id: serial("id").primaryKey(),
  recipeId: integer("recipe_id").notNull().references(() => recipes.id),
  name: text("name").notNull(),
  quantity: text("quantity"),
  unit: text("unit"),
});

export const insertIngredientSchema = createInsertSchema(ingredients).omit({
  id: true,
});

export type InsertIngredient = z.infer<typeof insertIngredientSchema>;
export type Ingredient = typeof ingredients.$inferSelect;

export const steps = pgTable("steps", {
  id: serial("id").primaryKey(),
  recipeId: integer("recipe_id").notNull().references(() => recipes.id),
  stepNumber: integer("step_number").notNull(),
  instruction: text("instruction").notNull(),
});

export const insertStepSchema = createInsertSchema(steps).omit({
  id: true,
});

export type InsertStep = z.infer<typeof insertStepSchema>;
export type Step = typeof steps.$inferSelect;

export const folders = pgTable("folders", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id),
  name: text("name").notNull(),
});

export const insertFolderSchema = createInsertSchema(folders).omit({
  id: true,
});

export type InsertFolder = z.infer<typeof insertFolderSchema>;
export type Folder = typeof folders.$inferSelect;

export const folderRecipes = pgTable("folder_recipes", {
  folderId: integer("folder_id").notNull().references(() => folders.id),
  recipeId: integer("recipe_id").notNull().references(() => recipes.id),
}, (table) => {
  return {
    pk: primaryKey(table.folderId, table.recipeId),
  };
});

export const insertFolderRecipeSchema = createInsertSchema(folderRecipes);

export type InsertFolderRecipe = z.infer<typeof insertFolderRecipeSchema>;
export type FolderRecipe = typeof folderRecipes.$inferSelect;

export const tags = pgTable("tags", {
  id: serial("id").primaryKey(),
  name: text("name").notNull().unique(),
});

export const insertTagSchema = createInsertSchema(tags).omit({
  id: true,
});

export type InsertTag = z.infer<typeof insertTagSchema>;
export type Tag = typeof tags.$inferSelect;

export const recipeTags = pgTable("recipe_tags", {
  recipeId: integer("recipe_id").notNull().references(() => recipes.id),
  tagId: integer("tag_id").notNull().references(() => tags.id),
}, (table) => {
  return {
    pk: primaryKey(table.recipeId, table.tagId),
  };
});

export const insertRecipeTagSchema = createInsertSchema(recipeTags);

export type InsertRecipeTag = z.infer<typeof insertRecipeTagSchema>;
export type RecipeTag = typeof recipeTags.$inferSelect;

export const favorites = pgTable("favorites", {
  userId: integer("user_id").notNull().references(() => users.id),
  recipeId: integer("recipe_id").notNull().references(() => recipes.id),
}, (table) => {
  return {
    pk: primaryKey(table.userId, table.recipeId),
  };
});

export const insertFavoriteSchema = createInsertSchema(favorites);

export type InsertFavorite = z.infer<typeof insertFavoriteSchema>;
export type Favorite = typeof favorites.$inferSelect;
