import express from 'express';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = 5000;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(express.static(path.join(__dirname, 'public')));

// In-memory storage
const recipes = [
  {
    id: 1,
    userId: 1,
    title: "Spaghetti Carbonara",
    description: "A classic Italian pasta dish with eggs, cheese, pancetta, and black pepper.",
    imageUrl: "https://www.allrecipes.com/thmb/Vg2cRidr2zcYhWGvPD8M18xM_WY=/1500x0/filters:no_upscale():max_bytes(150000):strip_icc()/11973-spaghetti-carbonara-ii-DDMFS-4x3-6edea51e421e4457ac0c3269f3be5157.jpg",
    prepTime: "10 minutes",
    cookTime: "15 minutes",
    sourceUrl: "https://example.com/carbonara",
    notes: "Use freshly grated Pecorino Romano for best results.",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 2,
    userId: 1,
    title: "Chicken Tikka Masala",
    description: "A flavorful Indian curry dish with marinated chicken in a creamy tomato sauce.",
    imageUrl: "https://www.seriouseats.com/thmb/DbQHUK2yNCALBnZE-H1M2AKLkok=/1500x0/filters:no_upscale():max_bytes(150000):strip_icc()/chicken-tikka-masala-for-the-grill-recipe-hero-2_1-cb493f49e30140efbffec162d5f2d1d7.JPG",
    prepTime: "30 minutes",
    cookTime: "45 minutes",
    sourceUrl: "https://example.com/tikkamasala",
    notes: "Marinate the chicken overnight for best flavor.",
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

const ingredients = [
  { id: 1, recipeId: 1, name: "Spaghetti", quantity: "1", unit: "pound" },
  { id: 2, recipeId: 1, name: "Eggs", quantity: "4", unit: "large" },
  { id: 3, recipeId: 1, name: "Pancetta", quantity: "8", unit: "ounces" },
  { id: 4, recipeId: 1, name: "Pecorino Romano", quantity: "1", unit: "cup" },
  { id: 5, recipeId: 1, name: "Black pepper", quantity: "1", unit: "teaspoon" },

  { id: 6, recipeId: 2, name: "Chicken breast", quantity: "2", unit: "pounds" },
  { id: 7, recipeId: 2, name: "Yogurt", quantity: "1", unit: "cup" },
  { id: 8, recipeId: 2, name: "Tomato sauce", quantity: "2", unit: "cups" },
  { id: 9, recipeId: 2, name: "Heavy cream", quantity: "1", unit: "cup" },
  { id: 10, recipeId: 2, name: "Garam masala", quantity: "2", unit: "tablespoons" }
];

const steps = [
  { id: 1, recipeId: 1, stepNumber: 1, instruction: "Cook spaghetti according to package directions." },
  { id: 2, recipeId: 1, stepNumber: 2, instruction: "In a large bowl, whisk together eggs and cheese." },
  { id: 3, recipeId: 1, stepNumber: 3, instruction: "Cook pancetta until crispy." },
  { id: 4, recipeId: 1, stepNumber: 4, instruction: "Combine pasta with egg mixture and pancetta. Season with black pepper." },

  { id: 5, recipeId: 2, stepNumber: 1, instruction: "Marinate chicken in yogurt and spices for at least 1 hour." },
  { id: 6, recipeId: 2, stepNumber: 2, instruction: "Grill or broil chicken until cooked through." },
  { id: 7, recipeId: 2, stepNumber: 3, instruction: "Prepare sauce by simmering tomato sauce with spices." },
  { id: 8, recipeId: 2, stepNumber: 4, instruction: "Add cream to the sauce and simmer." },
  { id: 9, recipeId: 2, stepNumber: 5, instruction: "Cut chicken into pieces and add to the sauce." }
];

const tags = [
  { id: 1, name: "Italian" },
  { id: 2, name: "Pasta" },
  { id: 3, name: "Quick" },
  { id: 4, name: "Indian" },
  { id: 5, name: "Chicken" },
  { id: 6, name: "Curry" }
];

const recipeTags = [
  { recipeId: 1, tagId: 1 },
  { recipeId: 1, tagId: 2 },
  { recipeId: 1, tagId: 3 },
  { recipeId: 2, tagId: 4 },
  { recipeId: 2, tagId: 5 },
  { recipeId: 2, tagId: 6 }
];

// Routes
app.post('/api/verify-password', (req, res) => {
  const { password } = req.body;

  if (password === "happy days") {
    res.json({ success: true });
  } else {
    res.status(401).json({
      success: false,
      message: "Incorrect password"
    });
  }
});

app.get('/api/recipes', (req, res) => {
  res.json(recipes);
});

app.get('/api/recipes/:id', (req, res) => {
  const recipeId = parseInt(req.params.id);
  const recipe = recipes.find(r => r.id === recipeId);

  if (!recipe) {
    return res.status(404).json({ message: "Recipe not found" });
  }

  const recipeIngredients = ingredients.filter(i => i.recipeId === recipeId);
  const recipeSteps = steps.filter(s => s.recipeId === recipeId);
  const recipeTagIds = recipeTags.filter(rt => rt.recipeId === recipeId).map(rt => rt.tagId);
  const recipeTags = tags.filter(t => recipeTagIds.includes(t.id));

  res.json({
    recipe,
    ingredients: recipeIngredients,
    steps: recipeSteps,
    tags: recipeTags,
    isFavorite: false
  });
});

app.post('/api/recipes/import', (req, res) => {
  const { url } = req.body;

  if (!url) {
    return res.status(400).json({ message: "URL is required" });
  }

  // Simulate recipe parsing
  setTimeout(() => {
    // Return a mock parsed recipe
    res.json({
      recipe: {
        title: "Imported Recipe from " + url,
        description: "This is a simulated imported recipe for demonstration purposes.",
        imageUrl: "https://via.placeholder.com/300",
        prepTime: "20 minutes",
        cookTime: "30 minutes",
        sourceUrl: url,
        notes: ""
      },
      ingredients: [
        { name: "Ingredient 1", quantity: "1", unit: "cup" },
        { name: "Ingredient 2", quantity: "2", unit: "tablespoons" },
        { name: "Ingredient 3", quantity: "3", unit: "ounces" }
      ],
      steps: [
        { stepNumber: 1, instruction: "Step 1 instruction" },
        { stepNumber: 2, instruction: "Step 2 instruction" },
        { stepNumber: 3, instruction: "Step 3 instruction" }
      ],
      tags: ["Tag1", "Tag2"]
    });
  }, 2000);
});

app.post('/api/recipes', (req, res) => {
  const { recipe, ingredients, steps, tags } = req.body;

  // Simulate creating a new recipe
  const newRecipe = {
    ...recipe,
    id: recipes.length + 1,
    userId: 1,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  recipes.push(newRecipe);

  res.json(newRecipe);
});

app.get('/api/folders', (req, res) => {
  // Return mock folders
  res.json([
    { id: 1, userId: 1, name: "Italian Recipes" },
    { id: 2, userId: 1, name: "Asian Cuisine" },
    { id: 3, userId: 1, name: "Quick Meals" }
  ]);
});

app.get('/api/favorites', (req, res) => {
  // Return mock favorites (just the first recipe)
  res.json([recipes[0]]);
});

// Serve the HTML file
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Create public directory and index.html
const publicDir = path.join(__dirname, 'public');
if (!fs.existsSync(publicDir)) {
  fs.mkdirSync(publicDir);
}

const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>RecipeSafe Demo</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #ff6b35;
    }
    .login-form {
      margin-bottom: 20px;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    .recipe-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 20px;
    }
    .recipe-card {
      border: 1px solid #ddd;
      border-radius: 5px;
      overflow: hidden;
      transition: transform 0.2s;
    }
    .recipe-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    .recipe-image {
      width: 100%;
      height: 150px;
      object-fit: cover;
    }
    .recipe-content {
      padding: 15px;
    }
    .recipe-title {
      margin-top: 0;
      color: #333;
    }
    .recipe-description {
      color: #666;
      font-size: 14px;
    }
    button {
      background-color: #ff6b35;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background-color: #e85a2a;
    }
    input {
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      margin-right: 10px;
    }
  </style>
</head>
<body>
  <h1>RecipeSafe Demo</h1>

  <div class="login-form" id="login-form">
    <h2>Enter Password</h2>
    <p>Use the password: "happy days" to access the recipes</p>
    <input type="password" id="password" placeholder="Enter password">
    <button onclick="verifyPassword()">Login</button>
    <p id="login-error" style="color: red; display: none;">Incorrect password</p>
  </div>

  <div id="recipes-container" style="display: none;">
    <h2>Your Recipes</h2>
    <div class="recipe-grid" id="recipe-grid"></div>
  </div>

  <script>
    // Verify password
    async function verifyPassword() {
      const password = document.getElementById('password').value;

      try {
        const response = await fetch('/api/verify-password', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ password })
        });

        const data = await response.json();

        if (data.success) {
          document.getElementById('login-form').style.display = 'none';
          document.getElementById('recipes-container').style.display = 'block';
          loadRecipes();
        } else {
          document.getElementById('login-error').style.display = 'block';
        }
      } catch (error) {
        console.error('Error:', error);
      }
    }

    // Load recipes
    async function loadRecipes() {
      try {
        const response = await fetch('/api/recipes');
        const recipes = await response.json();

        const recipeGrid = document.getElementById('recipe-grid');
        recipeGrid.innerHTML = '';

        recipes.forEach(recipe => {
          const recipeCard = document.createElement('div');
          recipeCard.className = 'recipe-card';

          recipeCard.innerHTML = \`
            <img src="\${recipe.imageUrl}" alt="\${recipe.title}" class="recipe-image">
            <div class="recipe-content">
              <h3 class="recipe-title">\${recipe.title}</h3>
              <p class="recipe-description">\${recipe.description}</p>
              <button onclick="viewRecipe(\${recipe.id})">View Recipe</button>
            </div>
          \`;

          recipeGrid.appendChild(recipeCard);
        });
      } catch (error) {
        console.error('Error:', error);
      }
    }

    // View recipe details
    async function viewRecipe(id) {
      try {
        const response = await fetch(\`/api/recipes/\${id}\`);
        const recipeData = await response.json();

        const recipesContainer = document.getElementById('recipes-container');
        recipesContainer.innerHTML = \`
          <button onclick="backToRecipes()">← Back to Recipes</button>
          <h2>\${recipeData.recipe.title}</h2>
          <img src="\${recipeData.recipe.imageUrl}" alt="\${recipeData.recipe.title}" style="max-width: 100%; max-height: 300px; object-fit: cover;">
          <p>\${recipeData.recipe.description}</p>

          <div style="display: flex; gap: 20px; margin: 20px 0;">
            <div>
              <h3>Prep Time</h3>
              <p>\${recipeData.recipe.prepTime}</p>
            </div>
            <div>
              <h3>Cook Time</h3>
              <p>\${recipeData.recipe.cookTime}</p>
            </div>
          </div>

          <h3>Ingredients</h3>
          <ul>
            \${recipeData.ingredients.map(ingredient => \`
              <li>\${ingredient.quantity} \${ingredient.unit} \${ingredient.name}</li>
            \`).join('')}
          </ul>

          <h3>Instructions</h3>
          <ol>
            \${recipeData.steps.map(step => \`
              <li>\${step.instruction}</li>
            \`).join('')}
          </ol>

          <h3>Notes</h3>
          <p>\${recipeData.recipe.notes || 'No notes'}</p>

          <h3>Source</h3>
          <p><a href="\${recipeData.recipe.sourceUrl}" target="_blank">\${recipeData.recipe.sourceUrl}</a></p>
        \`;
      } catch (error) {
        console.error('Error:', error);
      }
    }

    // Back to recipes list
    function backToRecipes() {
      document.getElementById('recipes-container').innerHTML = \`
        <h2>Your Recipes</h2>
        <div class="recipe-grid" id="recipe-grid"></div>
      \`;
      loadRecipes();
    }
  </script>
</body>
</html>
`;

fs.writeFileSync(path.join(publicDir, 'index.html'), htmlContent);

// Start the server
app.listen(PORT, () => {
  console.log(`Server running at http://localhost:${PORT}`);
});
