import { StyleSheet, TouchableOpacity } from 'react-native';
import { useState } from 'react';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';

export default function HomeScreen() {
  const [counter, setCounter] = useState(0);

  const incrementCounter = () => {
    setCounter(counter + 1);
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.headerContainer}>
        <ThemedText type="title">Test APK</ThemedText>
        <ThemedText type="subtitle">React Native Android App</ThemedText>
      </ThemedView>

      <ThemedView style={styles.counterContainer}>
        <ThemedText type="subtitle">Counter: {counter}</ThemedText>
        <TouchableOpacity
          style={styles.button}
          onPress={incrementCounter}
        >
          <ThemedText style={styles.buttonText}>Increment</ThemedText>
        </TouchableOpacity>
      </ThemedView>

      <ThemedView style={styles.infoContainer}>
        <ThemedText>This is a simple test application built with Expo.</ThemedText>
        <ThemedText>No Java installation required!</ThemedText>
      </ThemedView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  counterContainer: {
    alignItems: 'center',
    marginVertical: 30,
  },
  button: {
    backgroundColor: '#4287f5',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
    marginTop: 20,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  infoContainer: {
    alignItems: 'center',
    marginTop: 40,
  },
});
