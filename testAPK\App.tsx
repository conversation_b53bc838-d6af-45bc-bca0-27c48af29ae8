/**
 * Test APK - React Native Android App
 *
 * @format
 */

import React from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  useColorScheme,
  View,
} from 'react-native';

function App(): React.JSX.Element {
  const isDarkMode = useColorScheme() === 'dark';
  const [counter, setCounter] = React.useState(0);

  const backgroundStyle = {
    backgroundColor: isDarkMode ? '#1a1a1a' : '#f5f5f5',
    flex: 1,
  };

  const textColor = {
    color: isDarkMode ? '#ffffff' : '#000000',
  };

  const incrementCounter = () => {
    setCounter(counter + 1);
  };

  return (
    <SafeAreaView style={backgroundStyle}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={backgroundStyle.backgroundColor}
      />
      <View style={styles.container}>
        <Text style={[styles.title, textColor]}>
          Test APK
        </Text>
        <Text style={[styles.subtitle, textColor]}>
          React Native Android App
        </Text>

        <View style={styles.counterContainer}>
          <Text style={[styles.counterText, textColor]}>
            Counter: {counter}
          </Text>
          <TouchableOpacity
            style={styles.button}
            onPress={incrementCounter}
          >
            <Text style={styles.buttonText}>Increment</Text>
          </TouchableOpacity>
        </View>

        <Text style={[styles.footer, textColor]}>
          This is a simple test application
        </Text>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 18,
    marginBottom: 40,
    textAlign: 'center',
  },
  counterContainer: {
    alignItems: 'center',
    marginVertical: 30,
  },
  counterText: {
    fontSize: 24,
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#4287f5',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
    elevation: 3,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  footer: {
    marginTop: 40,
    fontSize: 14,
    opacity: 0.7,
  },
});

export default App;
